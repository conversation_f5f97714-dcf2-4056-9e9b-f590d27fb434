<template>
    <div>
        <div class="d-flex align-items-center">
            <h1>
                <ol class="breadcrumb">
                    <li
                        v-for="breadcrumb in breadcrumbs.slice(0, -1)"
                        class="breadcrumb-item"
                    >
                        <router-link :to="breadcrumb">
                            {{ breadcrumb.meta.title }}
                        </router-link>
                    </li>
                    <li class="breadcrumb-item active">
                        {{ lastRouteTitle }}
                    </li>

                    <li v-if="!disableHelp">
                        <button
                            type="button"
                            title="Help"
                            class="btn btn-secondary ms-1"
                            @click="onToggleDescription"
                        >
                            <Icona name="icn-question" />
                        </button>
                    </li>
                    <slot name="afterHelp" />
                </ol>
            </h1>

            <slot name="afterHeader" />
        </div>
        <div
            class="card collapse mb-1"
            :class="{show: descriptionOpened}"
        >
            <div
                class="card-body"
                style="max-height: 20em; overflow-x: auto;"
                v-html="descriptionFinal"
            />
            <div
                v-show="canEdit && descriptionOpened"
                class="card-footer font-italic"
            >
                <a
                    target="_blank"
                    :href="descriptionEditLink"
                >
                    <Icona name="icn-pencil" /> Edit description
                </a>
                <slot name="footer" />
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue'
import { Help } from '@/types'
import { Icona } from '@/components'
import { useRoute } from 'vue-router'
import { useAuthUser } from '@/utils/auth-user'
import { useTitleBreadcrumbs } from '@/utils/title-breadcrumbs'
import { useFetch } from '@/utils/fetch'

const $props = withDefaults(defineProps<{
    disableHelp?: boolean
    descriptionOpenedAtLoad?: boolean
    title?: string
    description?: string
    elements?: string
}>(), {
    title: undefined,
    description: undefined,
    elements: undefined,
})

const $emit = defineEmits<{
    helpLoaded: [help: Help]
}>()

// Composables
const authUser = useAuthUser()
const route = useRoute()
const titleBreadcrumbs = useTitleBreadcrumbs()
const $fetch = useFetch()

const help = ref<Help>({} as Help)
const descriptionOpened = ref($props.descriptionOpenedAtLoad)

const breadcrumbs = computed(() => {
    return titleBreadcrumbs.breadcrumbs.value
})

const lastRouteTitle = computed(() => {
    if ($props.title) {
        return $props.title
    }

    return breadcrumbs.value.slice(-1)[0].meta.title as string
})

const descriptionFinal = computed(() => {
    if ($props.description) {
        return $props.description
    }

    if ((help.value.description || '').length === 0) {
        return '<em v-else>no description yet</em>'
    }

    return help.value.description
})

const descriptionEditLink = computed(() => {
    return `/back/help/update?url=${encodeURI(route.path)}`
})

const canEdit = computed(() => {
    return authUser.can('editHelp')
})

async function onToggleDescription() {
    if (descriptionOpened.value) {
        descriptionOpened.value = false
    } else {
        help.value = await $fetch(`/back/help/get?url=${encodeURI(route.path)}`)
        $emit('helpLoaded', help.value)
        descriptionOpened.value = true
    }
}

watch(() => route.path, async () => {
    titleBreadcrumbs.setMatchedRoutes(route.matched)
}, { immediate: true })
</script>
