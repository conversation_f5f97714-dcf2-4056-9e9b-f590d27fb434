<template>
    <div v-if="!disableBreadcrumbs" class="container">
        <Breadcrumbs :disableHelp="disableHelp">
            <template #afterHelp>
                <slot name="afterHelp" />
            </template>
            <template #afterHeader>
                <slot name="afterHeader" />
            </template>
        </Breadcrumbs>
    </div>

    <div :class="widthClass">
        <div class="card card-primary shadow-sm">
            <div class="card-body">
                <slot />
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import Breadcrumbs from './breadcrumbs.vue'

type CardWidth = 'normal' | 'wide'

const $props = withDefaults(defineProps<{
    width?: CardWidth
    disableHelp?: boolean
    disableBreadcrumbs?: boolean
}>(), {
    width: 'normal',
})

const widthClass = computed(() => {
    const classMap = {
        normal: 'container',
        wide: 'container-fluid',
    }

    return classMap[$props.width] || 'container'
})
</script>
