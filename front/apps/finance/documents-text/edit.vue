<template>
    <div class="row my-5 documents-text">
        <DocumentsAwaitApproveLink
            :await-approve-count="awaitApproveCount"
            href="/finance/documents-text/new"
            name="New"
        />
        <div class="col-md-2">
            <strong class="d-inline-block mb-2 text-primary">&nbsp;</strong>
            <pre class="pt-4 sticky-top">{{ fullText }}</pre>
        </div>
        <div class="col-md-8">
            <strong class="d-block mb-2 text-primary">
                <span
                    v-if="info.country"
                    class="badge bg-warning float-end"
                >{{ info.country }}</span>
                <router-link :to="{path: '/finance/documents-text/edit/' + userDocumentId}">{{ info.siteUser }}</router-link>
                &nbsp;
                <router-link
                    v-if="info.siteUser"
                    :to="{name: 'player', params: {siteUser: info.siteId + '-' + info.userId}}"
                    target="_blank"
                >Player</router-link>
                &nbsp;
                <router-link
                    v-if="info.siteUser"
                    :to="{name: 'documents', query: {siteId: info.siteId, userId: info.userId}}"
                    target="_blank"
                >Documents</router-link>
            </strong>
            <div
                class="border rounded progress-bar progress-bar-striped progress-bar-animated bg-secondary"
                role="progressbar"
                aria-valuenow="75"
                aria-valuemin="0"
                aria-valuemax="100"
            >
                <canvas
                    ref="canvas"
                    style="width: 100%"
                />
            </div>
            <div
                v-if="otherDocuments"
                class="row"
            >
                <div
                    v-for="doc in otherDocuments"
                    class="col-xl-3 position-relative"
                >
                    <a
                        v-if="doc.imgLoaded && doc.approved === null"
                        href="#"
                        @click.prevent="ignoreDocument(doc)"
                    >
                        <Icona
                            name="icn-ban"
                            class=" position-absolute mt-4 ms-2 text-danger"
                            aria-hidden="true"
                        />
                    </a>
                    <router-link
                        :href="'/finance/documents-text/edit/' + doc.id"
                        :to="{path: '/finance/documents-text/' + (directEdit ? 'edit' : 'new') + '/' + doc.id}"
                    >
                        <img
                            :src="doc.imgLoaded ? doc.url : undefined"
                            :class="{'bg-success': doc.approved === true, 'bg-danger': doc.approved === false}"
                            style="width: 100%"
                            class="img-responsive img-thumbnail mt-3"
                            @load="showNextDocument"
                            @error="showNextDocument"
                        >
                    </router-link>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="mb-3 sticky-top">
                <strong class="d-inline-block mb-2 text-primary">&nbsp;</strong>
                <div class="input-group mb-3 flex-nowrap">
                    <button
                        class="btn btn-outline-secondary focus-disabled"
                        type="button"
                        @click="switchMode(MODE_CHOOSE_SURNAME)"
                    >
                        <Icona
                            :name="mode === MODE_CHOOSE_SURNAME ? 'icn-hand-finger-up' : 'icn-wrench'"
                        />
                    </button>
                    <TextInputLiveSearch
                        id="surname"
                        name="surname"
                        :value="boxSurname.text"
                        :enabled="boxSurname.text != null"
                        :isInvalid="loaded && approved === true && !boxSurname.text"
                        :liveSearchUrl="'/finance/documents-text/autocomplete-surname'"
                        inputClass="text-uppercase rounded-0"
                        placeholder="Surname"
                        class="form-control form-control-md"

                        @input="boxSurname.text = $event"
                        @change="boxSurname.text = $event"
                    />
                    <button
                        :disabled="boxSurname.text == null"
                        class="btn btn-outline-secondary focus-disabled"
                        type="button"
                        @click="banType(TYPE_SURNAME)"
                    >
                        <Icona name="icn-ban" />
                    </button>
                </div>
                <div class="input-group mb-3 flex-nowrap">
                    <button
                        class="btn btn-outline-secondary focus-disabled"
                        type="button"
                        @click="switchMode(MODE_CHOOSE_NAME)"
                    >
                        <Icona
                            :name="mode === MODE_CHOOSE_NAME ? 'icn-hand-finger-up' : 'icn-wrench'"
                        />
                    </button>
                    <TextInputLiveSearch
                        id="name"
                        name="name"
                        :value="boxName.text"
                        :enabled="boxName.text != null"
                        inputClass="text-uppercase rounded-0"
                        liveSearchUrl="/finance/documents-text/autocomplete-name"

                        placeholder="Name"
                        class="form-control form-control-md"

                        @input="boxName.text = $event"
                        @change="boxName.text = $event"
                    />
                    <button
                        :disabled="boxName.text == null"
                        class="btn btn-outline-secondary focus-disabled"
                        type="button"
                        @click="banType(TYPE_NAME)"
                    >
                        <Icona name="icn-ban" />
                    </button>
                </div>
                <div class="input-group mb-3 flex-nowrap">
                    <button
                        class="btn btn-outline-secondary focus-disabled"
                        type="button"
                        @click="switchMode(MODE_CHOOSE_PATRONYMIC)"
                    >
                        <Icona
                            :name="mode === MODE_CHOOSE_PATRONYMIC ? 'icn-hand-finger-up' : 'icn-wrench'"
                        />
                    </button>
                    <TextInputLiveSearch
                        id="patronymic"
                        name="patronymic"
                        :value="boxPatronymic.text"
                        :enabled="boxPatronymic.text != null"
                        inputClass="text-uppercase rounded-0"
                        liveSearchUrl="/finance/documents-text/autocomplete-patronymic"

                        placeholder="Patronymic"
                        class="form-control form-control-md"

                        @input="boxPatronymic.text = $event"
                        @change="boxPatronymic.text = $event"
                    />
                    <button
                        :disabled="boxPatronymic.text == null"
                        class="btn btn-outline-secondary focus-disabled"
                        type="button"
                        @click="banType(TYPE_PATRONYMIC)"
                    >
                        <Icona name="icn-ban" />
                    </button>
                </div>
                <div
                    v-if="!directEdit && Object.keys(prevFullName).length > 0"
                    class="input-group mb-3 flex-nowrap"
                >
                    <pre
                        class="form-control form-control-md text-uppercase"
                        style="height: auto; white-space: normal;"
                    ><span
                        v-for="(v, k) in prevFullName"
                        :class="[parseInt($data[k].type) === TYPE_TRASH ? 'text-secondary' : ($data[k].text.toLowerCase().trim() === v ? 'fw-normal' : 'fw-bold')]"
                        class="d-block"
                    >
                            {{ v }}
                    </span></pre>
                    <button
                        class="btn btn-outline-secondary"
                        type="button"
                        @click="prevFullNameLoad()"
                    >
                        <Icona name="icn-arrow-up" />
                    </button>
                </div>
                <div class="input-group mb-3 flex-nowrap">
                    <div
                        class="btn-group btn-group-toggle w-100"
                        data-toggle="buttons"
                    >
                        <label
                            :class="approved === false ? 'active' : ''"
                            class="btn btn-outline-secondary pointer"
                            @click="approved = false"
                        >
                            Ignore unreadable
                        </label>
                        <label
                            :class="approved === true ? 'active' : ''"
                            class="btn btn-outline-secondary pointer"
                            @click="approved = true"
                        >
                            Approve
                        </label>
                    </div>
                </div>
                <button
                    :disabled="!formValid"
                    type="button"
                    class="btn btn-primary btn-lg d-block"
                    @click="formValid && sendForm()"
                >
                    {{ directEdit ? 'SAVE' : 'SEND' }}
                </button>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { Painter } from './painter'
import { TextBoundingBox } from './text-bounding-box'
import TextInputLiveSearch from '@/components/form/types/text-input-live-search.vue'
import DocumentsAwaitApproveLink from '../documents-await-approve-link.vue'
import { defineComponent } from 'vue'
import { Icona } from '@/components'
import { PointType } from '@/types'
import type { RouteLocationNormalized, NavigationGuardNext } from 'vue-router'

const MODE_DEFAULT = 'default' as const
const MODE_CHOOSE_SURNAME = 'surname' as const
const MODE_CHOOSE_NAME = 'name' as const
const MODE_CHOOSE_PATRONYMIC = 'patronymic' as const

const TYPE_NULL = null
const TYPE_TRASH = 0
const TYPE_SURNAME = 1
const TYPE_NAME = 2
const TYPE_PATRONYMIC = 3

const CONTROL_SIZE = 6

type Mode = typeof MODE_DEFAULT | typeof MODE_CHOOSE_SURNAME | typeof MODE_CHOOSE_NAME | typeof MODE_CHOOSE_PATRONYMIC
type BoxType = typeof TYPE_NULL | typeof TYPE_TRASH | typeof TYPE_SURNAME | typeof TYPE_NAME | typeof TYPE_PATRONYMIC

interface DocumentInfo {
    siteId: number
    userId: number
    siteUser: string
    country?: string
}

interface OtherDocument {
    id: string
    url: string
    approved: boolean | null
    imgLoaded: boolean
}

interface ApiResponse {
    data: {
        url: string
        awaitApproveCount: number
        fullText: string
        siteId: number
        userId: number
        siteUser: string
        country?: string
        userDocumentId: string
        approved: boolean | null
        otherDocuments: Omit<OtherDocument, 'imgLoaded'>[]
        textBoundingBoxes: number[][]
        textBoundingBoxesManual?: number[][]
    }
}

const DocumentsTextEdit = defineComponent({
    name: 'DocumentsTextEdit',
    components: {
        Icona,
        TextInputLiveSearch,
        DocumentsAwaitApproveLink,
    },
    beforeRouteEnter (to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) {
        next(vm => (vm as InstanceType<typeof DocumentsTextEdit>).reload(from.params.userDocumentId as string, to.params.userDocumentId as string))
    },
    beforeRouteUpdate (to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) {
        if (from.params.userDocumentId || !to.params.userDocumentId) {
            const prev = this.prevFullName
            this.reload(from.params.userDocumentId as string, to.params.userDocumentId as string)
            this.prevFullName = prev
        }
        next()
    },
    props: {
        directEdit: {
            type: Boolean,
            default: false,
        },
    },
    data () {
        return {
            awaitApproveCount: 0,
            MODE_DEFAULT,
            MODE_CHOOSE_SURNAME,
            MODE_CHOOSE_NAME,
            MODE_CHOOSE_PATRONYMIC,
            TYPE_NULL,
            TYPE_TRASH,
            TYPE_SURNAME,
            TYPE_NAME,
            TYPE_PATRONYMIC,
            CONTROL_SIZE,
            info: {} as DocumentInfo,
            loaded: false,
            trashEnabled: false,
            userDocumentId: null,
            boxNew: new TextBoundingBox(TYPE_TRASH),
            boxSurname: new TextBoundingBox(TYPE_TRASH),
            boxName: new TextBoundingBox(TYPE_TRASH),
            boxPatronymic: new TextBoundingBox(TYPE_TRASH),
            prevFullName: {},
            predictedBoxes: [],
            mouse: { x: -1, y: -1 } as PointType,
            editableBox: undefined as TextBoundingBox | undefined,
            painter: undefined as Painter | undefined,
            fullText: undefined as string | undefined,
            mouseLeftDragging: false,
            mouseLeftIsDown: false,
            mouseRightIsDown: false,
            mouseRightDownPoint: false as PointType | false,
            mouseRightDragging: false,
            mode: MODE_DEFAULT as Mode,
            approved: undefined,
            activeControlDot: false,
            afterChoose: {
                [MODE_CHOOSE_SURNAME]: MODE_DEFAULT,
                [MODE_CHOOSE_NAME]: MODE_DEFAULT,
                [MODE_CHOOSE_PATRONYMIC]: MODE_DEFAULT,
            },
            nextMode: MODE_DEFAULT,
            otherDocuments: [] as OtherDocument[],
        }
    },
    computed: {
        boxes (): TextBoundingBox[] {
            return [this.boxNew, this.boxSurname, this.boxName, this.boxPatronymic, ...this.predictedBoxes]
        },
        formValid (): boolean {
            return Boolean(this.boxSurname.text) || this.approved === false
        },
    },
    methods: {
        reload (fromId: string | undefined, toId: string | undefined): void {
            const loadParams: Record<string, string> = {}
            Object.assign(this.$data, (this.$options.data as () => ComponentData)())
            if (toId) {
                loadParams.userDocumentId = toId
            }
            this.userDocumentId = toId || null

            this.painter = new Painter(this.$refs.canvas as HTMLCanvasElement)
            this.painter.on('mouseup', (e: Event) => this.mouseChoose(e as MouseEvent, this.mouseUpLeft, this.mouseUpRight))
            this.painter.on('mousemove', (e: Event) => this.mouseChoose(e as MouseEvent, this.mouseMove))
            this.painter.on('mousedown', (e: Event) => this.mouseChoose(e as MouseEvent, this.mouseDownLeft, this.mouseDownRight))

            this.$fetch('/finance/documents-text/view', loadParams)
                .then((resp: ApiResponse) => {
                    this.painter!.loadImage(resp.data.url).then(() => {
                        this.loaded = true
                        this.awaitApproveCount = resp.data.awaitApproveCount
                        this.fullText = resp.data.fullText
                        this.info = {
                            siteId: resp.data.siteId,
                            userId: resp.data.userId,
                            siteUser: resp.data.siteUser,
                            country: resp.data.country,
                        }
                        this.userDocumentId = resp.data.userDocumentId
                        if (!this.directEdit && !toId) {
                            this.$router.push({ params: { userDocumentId: this.userDocumentId } })
                        }
                        this.approved = resp.data.approved
                        this.otherDocuments = resp.data.otherDocuments.map((doc): OtherDocument => {
                            return { ...doc, imgLoaded: false }
                        })

                        const polygonToBox = (p: number[]): PointType[] => p.map((x, i) => i % 2 === 0 && p.slice(i, i + 2)).filter(x => x).map(d => {
                            return { x: d[0] * this.painter!.resize, y: d[1] * this.painter!.resize }
                        })
                        resp.data.textBoundingBoxes.forEach((b: number[]) => {
                            this.predictedBoxes.push(new TextBoundingBox(b[0], b[1], polygonToBox(b.slice(2))))
                        })
                        if (resp.data.textBoundingBoxesManual) {
                            resp.data.textBoundingBoxesManual.forEach((b: number[]) => {
                                if (b[0] === TYPE_SURNAME) {
                                    this.boxSurname.fill(-TYPE_SURNAME, b[1], polygonToBox(b.slice(2)))
                                } else if (b[0] === TYPE_NAME) {
                                    this.boxName.fill(-TYPE_NAME, b[1], polygonToBox(b.slice(2)))
                                } else if (b[0] === TYPE_PATRONYMIC) {
                                    this.boxPatronymic.fill(-TYPE_PATRONYMIC, b[1], polygonToBox(b.slice(2)))
                                }
                            })
                        }
                        if (this.approved === null) {
                            this.afterChoose = {
                                [MODE_CHOOSE_SURNAME]: MODE_CHOOSE_NAME,
                                [MODE_CHOOSE_NAME]: MODE_CHOOSE_PATRONYMIC,
                                [MODE_CHOOSE_PATRONYMIC]: MODE_DEFAULT,
                                [MODE_DEFAULT]: MODE_DEFAULT,
                            }
                            this.switchMode(MODE_CHOOSE_SURNAME)
                            this.approved = true
                        }
                        this.reDrawAll()
                        this.showNextDocument()
                    })
                })
                .catch((resp: Response) => resp.json())
        },
        isVisible (box: TextBoundingBox): boolean {
            return box.type === (TYPE_NULL && (this.trashEnabled || this.mode !== MODE_DEFAULT)) || (box.type !== null && box.type < TYPE_TRASH && this.mode === MODE_DEFAULT)
        },
        mouseChoose (e: MouseEvent, leftButtonHandler?: (x: number, y: number) => void, rightButtonHandler?: (x: number, y: number) => void): void {
            if (e.button === 0 && leftButtonHandler) {
                leftButtonHandler(...this.painter!.mousePosition(e))
            } else if (e.button === 2 && rightButtonHandler) {
                rightButtonHandler(...this.painter!.mousePosition(e))
            }
        },
        setBoxType (box: TextBoundingBox, type: BoxType): void {
            switch (type) {
                case TYPE_SURNAME:
                    this.boxSurname.fill(-type, box.text || '', box.polygon || [])
                    break
                case TYPE_NAME:
                    this.boxName.fill(-type, box.text || '', box.polygon || [])
                    break
                case TYPE_PATRONYMIC:
                    this.boxPatronymic.fill(-type, box.text || '', box.polygon || [])
                    break
                case TYPE_TRASH:
                    if (this.editableBox !== box) {
                        box.type = type
                    }
                    return
                case TYPE_NULL:
                    switch (box.type) {
                        case TYPE_SURNAME:
                            this.boxSurname.fill(TYPE_NULL, '', [])
                            break
                        case TYPE_NAME:
                            this.boxName.fill(TYPE_NULL, '', [])
                            break
                        case TYPE_PATRONYMIC:
                            this.boxPatronymic.fill(TYPE_NULL, '', [])
                            break
                    }
                    if (this.editableBox === box) {
                        this.editableBox = null
                    }
                    box.type = type
                    return
                default:
                    throw new Error('invalid type')
            }
            box.type = type
            this.boxes
                .filter(b => b !== box && b.type === type)
                .forEach(b => b.type = TYPE_NULL)
        },

        mouseDownRight (x: number, y: number): void {
            this.mouseRightIsDown = true
            this.mouseRightDragging = false
            this.mouseRightDownPoint = { x, y }
        },
        mouseUpRight (x: number, y: number): void {
            this.mouseRightIsDown = false

            const mp = this.mouseRightDownPoint
            this.mouseRightDownPoint = false

            const targets: Record<Mode, { box: keyof ComponentData, type: BoxType }> = {
                [MODE_CHOOSE_SURNAME]: { box: 'boxSurname', type: TYPE_SURNAME },
                [MODE_CHOOSE_NAME]: { box: 'boxName', type: TYPE_NAME },
                [MODE_CHOOSE_PATRONYMIC]: { box: 'boxPatronymic', type: TYPE_PATRONYMIC },
                [MODE_DEFAULT]: { box: 'boxNew', type: TYPE_NULL },
            }

            if (this.mode in targets && this.mode !== MODE_DEFAULT) {
                if (mp && Math.abs(x - mp.x) > 2 * CONTROL_SIZE && Math.abs(y - mp.y) > 2 * CONTROL_SIZE) {
                    this.predictedBoxes
                        .filter(box => box.type === targets[this.mode].type)
                        .forEach(box => this.setBoxType(box, TYPE_NULL))

                    const targetBox = this[targets[this.mode].box] as TextBoundingBox
                    targetBox.fill(-targets[this.mode].type as number, '', [
                        { x, y },
                        { x, y: mp.y },
                        { x: mp.x, y: mp.y },
                        { x: mp.x, y },
                    ])

                    this.switchMode(this.nextMode)
                }
            } else if (this.trashEnabled && this.editableBox === false && this.mode === MODE_DEFAULT && !this.mouseRightDragging) {
                this.boxes.filter(box => box.type === TYPE_NULL && box.haveInnerPoint(x, y))
                    .forEach(box => this.setBoxType(box, TYPE_TRASH))
            }
            this.reDrawAll()
        },

        mouseDownLeft (x: number, y: number): void {
            this.mouseLeftDragging = false
            this.mouseLeftIsDown = true

            this.boxes.some(box => {
                if (this.editableBox === box) {
                    this.activeControlDot = box.findControl(x, y, CONTROL_SIZE)
                    return true
                }

                return false
            })
        },
        mouseMove (x: number, y: number): void {
            this.mouse = { x, y }

            if (this.mouseRightIsDown && this.mode !== MODE_DEFAULT) {
                this.mouseRightDragging = true
                if (this.mouseRightDownPoint) {
                    this.boxNew.fill(TYPE_NULL, '', [
                        { x, y },
                        { x, y: this.mouseRightDownPoint.y },
                        { x: this.mouseRightDownPoint.x, y: this.mouseRightDownPoint.y },
                        { x: this.mouseRightDownPoint.x, y },
                    ])
                }
            } else {
                this.boxNew.fill(TYPE_TRASH, '', [])
            }

            if (this.mouseLeftIsDown === true) {
                this.mouseLeftDragging = true

                if (this.activeControlDot !== false && this.editableBox && this.editableBox.polygon) {
                    this.editableBox.polygon[this.activeControlDot] = this.mouse
                }
            } else {
                const overPolygon = this.boxes.some(box => {
                    if (
                        (this.mode === MODE_DEFAULT && box.type !== null && box.type < TYPE_TRASH) ||
                        (this.mode !== MODE_DEFAULT && box.type === TYPE_NULL) &&
                        this.isVisible(box) &&
                        ((this.editableBox === box && box.findControl(x, y, CONTROL_SIZE) !== false) || (this.editableBox !== box && box.haveInnerPoint(x, y)))
                    ) {
                        this.painter!.cursor('pointer')
                        return true
                    }
                    return false
                })
                if (!overPolygon) {
                    this.painter!.cursor('default')
                }
            }
            this.reDrawAll()
        },
        mouseUpLeft (x: number, y: number): void {
            this.mouseLeftIsDown = false
            this.activeControlDot = false
            if (this.mouseLeftDragging === true) {
                this.mouseLeftDragging = false
                return
            }

            const modeToType: Partial<Record<Mode, BoxType>> = {
                [MODE_CHOOSE_SURNAME]: TYPE_SURNAME,
                [MODE_CHOOSE_NAME]: TYPE_NAME,
                [MODE_CHOOSE_PATRONYMIC]: TYPE_PATRONYMIC,
            }

            if (this.mode in modeToType) {
                this.boxes.filter(b => b.type === TYPE_NULL).filter(b => b.haveInnerPoint(x, y)).some(box => {
                    const targetType = modeToType[this.mode]
                    if (targetType !== undefined) {
                        this.setBoxType(box, targetType)
                        this.switchMode(this.nextMode)
                    }
                    return true
                })
            } else {
                const activeFound = this.boxes.filter(b => b.type !== null && b.type < 0).filter(b => b.haveInnerPoint(x, y)).some(box => {
                    this.editableBox = box
                    return true
                })
                if (!activeFound) {
                    this.editableBox = false
                }
            }

            this.reDrawAll()
        },

        banType (type: BoxType): void {
            if (type === TYPE_SURNAME) {
                this.boxSurname.fill(TYPE_TRASH, '', [])
            } else if (type === TYPE_NAME) {
                this.boxName.fill(TYPE_TRASH, '', [])
            } else if (type === TYPE_PATRONYMIC) {
                this.boxPatronymic.fill(TYPE_TRASH, '', [])
            }

            this.boxes.filter(b => b.type === type).forEach(b => b.type = TYPE_NULL)
            this.switchMode(MODE_DEFAULT)
        },
        switchMode (mode: Mode): void {
            if (this.mode === MODE_DEFAULT && mode === MODE_DEFAULT) {
                this.editableBox = false
                this.reDrawAll()
                return
            } else if (this.mode === mode) {
                for (const m in this.afterChoose) {
                    this.afterChoose[m as Mode] = MODE_DEFAULT
                }
                this.switchMode(MODE_DEFAULT)
                return
            }

            this.mode = mode
            this.nextMode = this.afterChoose[mode] ? this.afterChoose[mode] : MODE_DEFAULT
            this.afterChoose[mode] = MODE_DEFAULT
            this.editableBox = false
            this.reDrawAll()
        },
        reDrawAll (): void {
            this.painter!.clear()
            this.boxes.forEach(b => this.isVisible(b) && this.drawBox(b))
        },
        drawBox (box: TextBoundingBox): void {
            if (box.polygon && box.polygon.length > 0) {
                this.painter!.drawPolygon(box.type !== null && box.type < TYPE_TRASH ? 'red' : 'black', box.polygon)
                if (this.editableBox === box) {
                    this.painter!.drawDots('rgba(44,62,80,0.5)', CONTROL_SIZE, box.polygon)
                }
                if (this.isTextVisible(box) && box.text && box.polygon.length >= 2) {
                    this.painter!.drawTextBetween(box.text, '14px Roboto', '#2c3e50', box.polygon[0], box.polygon[1])
                }
            }
        },
        isTextVisible (box: TextBoundingBox): boolean {
            return this.editableBox === box || (box.haveInnerPoint(this.mouse.x, this.mouse.y) && !this.mouseLeftDragging)
        },
        sendForm (): void {
            if (this.approved) {
                this.prevFullName = {};
                (['boxSurname', 'boxName', 'boxPatronymic'] as const).filter(itemName => {
                    const box = this[itemName] as TextBoundingBox
                    return box.type !== TYPE_TRASH
                }).forEach(itemName => {
                    const box = this[itemName] as TextBoundingBox
                    if (box.text) {
                        this.prevFullName[itemName] = box.text.toLowerCase().trim()
                    }
                })
            }
            const boxToPolygon = (box: TextBoundingBox): number[] => [
                -(box.type || 0),
                box.text || '',
                ...(!box.polygon ? [] : [
                    box.polygon[0].x, box.polygon[0].y,
                    box.polygon[1].x, box.polygon[1].y,
                    box.polygon[2].x, box.polygon[2].y,
                    box.polygon[3].x, box.polygon[3].y,
                ]).map(v => Math.round(v / this.painter!.resize)),
            ]

            const formData = {
                userDocumentId: this.userDocumentId,
                approved: this.approved,
                textBoundingBoxes: [] as number[][],
            }

            if (this.boxSurname.text) {
                formData.textBoundingBoxes.push(boxToPolygon(this.boxSurname))
            }
            if (this.boxName.text) {
                formData.textBoundingBoxes.push(boxToPolygon(this.boxName))
            }
            if (this.boxPatronymic.text) {
                formData.textBoundingBoxes.push(boxToPolygon(this.boxPatronymic))
            }

            this.$fetch('/finance/documents-text/update', formData).then(() => {
                if (!this.directEdit) {
                    this.$router.push('/finance/documents-text/new')
                }
            }).catch((resp: Response) => {
                return resp.json()
            })
        },
        prevFullNameLoad (): void {
            Object.keys(this.prevFullName).filter(itemName => {
                const box = this[itemName as keyof ComponentData] as TextBoundingBox
                return box.type !== TYPE_TRASH
            }).forEach(itemName => {
                const box = this[itemName as keyof ComponentData] as TextBoundingBox
                box.text = this.prevFullName[itemName]
            })
        },
        ignoreDocument (doc: OtherDocument): void {
            doc.approved = false
            this.$fetch('/finance/documents-text/update', {
                userDocumentId: doc.id,
                approved: false,
            }).catch(() => {
                doc.approved = null
            })
        },
        showNextDocument (): void {
            this.otherDocuments.filter(v => !v.imgLoaded).filter((v, i) => i === 0).forEach(v => v.imgLoaded = true)
        },
    },
})

export default DocumentsTextEdit
</script>

<style lang="scss">
.documents-text {
    .focus-disabled {
        outline: none !important;
        box-shadow: none;
    }

    #name, #surname, #patronymic {
        text-transform: uppercase;
    }
}
</style>
