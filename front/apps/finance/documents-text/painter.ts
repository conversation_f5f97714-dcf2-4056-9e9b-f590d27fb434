import { PointType } from '@/types.ts'

export class Painter {
    canvas: HTMLCanvasElement
    context: CanvasRenderingContext2D
    scanImage: HTMLImageElement = new Image()
    resize: number

    constructor (canvas: HTMLCanvasElement) {
        this.canvas = canvas
        this.context = this.canvas.getContext('2d')!
        this.context.clearRect(0, 0, this.canvas.width, this.canvas.height)
        this.canvas.addEventListener('contextmenu', e => e.preventDefault())
        this.resize = 1
    }

    on (event: string, callback: (e: Event) => void) {
        this.canvas.addEventListener(event, callback)
    }

    loadImage (url: string) {
        this.scanImage = new Image()
        const promise = new Promise(resolve => {
            this.scanImage.onload = () => {
                this.scanImage.classList.add('d-block')
                this.canvas.width = this.canvas.offsetWidth
                this.resize = this.canvas.width / this.scanImage.naturalWidth
                this.canvas.height = this.resize * this.scanImage.naturalHeight
                resolve(this.scanImage)
            }
        })

        this.scanImage.src = url
        return promise
    }

    mousePosition (e: MouseEvent) {
        const { clientX, clientY } = e
        const { top: canvasTop, left: canvasLeft } = this.canvas.getBoundingClientRect()
        return [clientX - canvasLeft, clientY - canvasTop]
    }

    clear () {
        this.context.clearRect(0, 0, this.canvas.width, this.canvas.height)
        if (this.scanImage !== undefined) {
            this.context.drawImage(this.scanImage, 0, 0, this.canvas.width, this.canvas.height)
        }
    }

    cursor (cursorStyle: string) {
        this.canvas.style.cursor = cursorStyle
    }

    drawPolygon (color: string, polygon: PointType[]) {
        this.context.beginPath();
        [...polygon, polygon[0]].forEach(p => this.context.lineTo(p.x, p.y))
        this.context.strokeStyle = color
        this.context.stroke()
        this.context.closePath()
        this.context.restore()
        this.context.save()
    }

    drawDots (color: string, size: number, dots: PointType[]) {
        dots.forEach(p => {
            this.context.save()
            this.context.translate(p.x, p.y)
            this.context.fillStyle = color
            this.context.fillRect(-size, -size, 2 * size, 2 * size)
            this.context.restore()
        })
        this.context.save()
    }

    drawTextBetween (text: string, font: string, color: string, left: PointType, right: PointType) {
        this.context.textAlign = 'center'
        const x = left.x + (right.x - left.x) / 2
        const y = left.y + (right.y - left.y) / 2 - 3
        this.context.translate(x, y)
        this.context.rotate(Math.tan((right.y - left.y) / (right.x - left.x)))

        this.context.font = font

        this.context.fillStyle = '#b3b3b3';
        [[-1, -1], [2, 0], [0, 2], [-2, 0]].forEach(([x, y]) => {
            this.context.translate(x, y)
            this.context.fillText(text, 0, 0)
        })
        this.context.translate(1, -1)

        this.context.fillStyle = color
        this.context.fillText(text, 0, 0)

        this.context.restore()
    }
}
