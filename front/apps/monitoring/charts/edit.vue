<template>
    <Card v-if="panel">
        <template #afterHelp>
            <li>
                <router-link
                    class="btn btn-primary ms-1"
                    :to="{name: 'panel', params: {panelId: $route.params.panelId}}"
                >
                    <Icona name="icn-eye" /> Preview
                </router-link>
            </li>
            <li>
                <button
                    type="button"
                    class="btn btn-danger ms-1"
                    @click="onPanelDelete"
                >
                    <Icona name="icn-delete" /> Delete
                </button>
            </li>
        </template>
        <FormGrid
            v-bind="panel.form"
            @change="panel.form.values = $event"
            @submit="onPanelSave"
        />

        <table class="charts-panel">
            <tr
                v-for="(cols, row) in panel.layout"
                :key="row"
            >
                <td
                    v-for="(chart, col) in cols"
                    :key="col"
                    :style="{width: Math.round(100 / cols.length * chart.col_span) + '%'}"
                    class="overflow-hidden"
                    :rowspan="Math.max(chart.row_span, 1)"
                    :colspan="Math.max(chart.col_span, 1)"
                >
                    <RouterLink
                        class="btn btn-lg d-block"
                        :to="chartEditRoute(panelId, row, col, 0)"
                        :draggable="!inProcess"
                        :class="getChartSelectorClasses(row, col, chart)"
                        @dragstart="onDragStart($event, row, col, chart)"
                        @dragover.prevent
                        @drop.prevent="onDrop(row, col, chart)"
                        @dragend.prevent="onDragEnd"
                    >
                        {{ chart.name === null ? '(empty)' : (chart.name || '&nbsp;') }}
                    </RouterLink>
                </td>
            </tr>
        </table>

        <template v-if="selectedChart">
            <template v-if="panel.layout[selectedChart.row][selectedChart.col].name === null">
                <hr>
                <FormGrid
                    v-bind="chartAddForm"
                    @change="chartAddForm.values = $event"
                    @submit="onChartAdd"
                />
            </template>
        </template>
        <template v-else>
            <p
                v-if="!selectedChart"
                class="font-italic text-center small"
            >
                Select chart to edit or drag to rearrange
            </p>
        </template>
    </Card>
    <br>
    <template v-if="panel && selectedPanelIdRowCol && panel.layout[selectedPanelIdRowCol.row][selectedPanelIdRowCol.col].name !== null">
        <EditChart
            :panelRowCol="selectedPanelIdRowCol"
            :serieIndex="serieIndex"
            :inProcess="inProcess"
            :showSqlButton="panel.showSqlButton"
            @chartDelete="onChartDelete"
            @panelUpdate="loadPanel"
        />
    </template>
</template>

<script lang="ts">

import { FormGrid, Icona } from '@/components'
import { defineComponent } from 'vue'
import { useTitleBreadcrumbs } from '@/utils/title-breadcrumbs.ts'
import { FormGridType, FormValue, Values, NextWithReload } from '@/types'
import EditChart, { chartEditRoute } from './edit-chart.vue'
import { Card } from '@/widgets'

interface RouteParams {
    panelId?: string
    rowCol?: string
    serieIndex?: string
}

interface RowCol {
    row: number
    col: number
}

export interface PanelIdRowCol extends RowCol {
    panelId: string
}

interface Chart {
    name: string | null
    row_span: number
    col_span: number
}

interface ChartPanel {
    form: FormGridType<{
        name: string
        cols: number
        rows: number
    }>
    layout: Chart[][]
    showSqlButton: boolean
}

interface ChartAddFormValues extends PanelIdRowCol {
    mode: number
    fromChart?: string
}

interface DraggedChart extends RowCol {
    chart: Chart
}

export default defineComponent({
    components: {
        Card,
        Icona,
        EditChart,
        FormGrid,
    },
    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(async (vm) => {
            await vm.init(to.params)
        })
    },
    props: {
        panelId: {
            type: [String],
            required: true,
        },
        rowCol: {
            type: [String],
            default: undefined,
        },
        serieIndex: {
            type: [String],
            default: undefined,
        },
    },
    setup() {
        return {
            titleBreadcrumbs: useTitleBreadcrumbs(),
        }
    },
    data () {
        return {
            inProcess: false,
            panel: undefined as ChartPanel | undefined,
            selectedChart: undefined as RowCol | undefined,
            chartAddForm: {} as FormGridType<ChartAddFormValues>,
            chartForm: {} as FormGridType,
            addSerieForm: {} as FormGridType,
            draggedChart: undefined as DraggedChart | undefined,
            focusOnFilter: undefined as FormValue | undefined,
        }
    },

    computed: {
        selectedPanelIdRowCol() {
            if (!this.selectedChart) {
                return undefined
            }

            return {
                panelId: this.panelId,
                col: this.selectedChart.col,
                row: this.selectedChart.row,
            }
        },
    },

    watch: {
        rowCol: {
            handler(rowCol: string | undefined) {
                if (!rowCol) {
                    this.selectedChart = undefined
                    return
                }
                const [row, col] = rowCol.split('-').map((v) => parseInt(v))
                this.selectedChart = { row, col }
            },
            immediate: true,
        },
        selectedPanelIdRowCol: {
            async handler (selectedPanelIdRowCol: PanelIdRowCol | undefined) {
                if (!selectedPanelIdRowCol) {
                    return
                }

                const { row, col } = selectedPanelIdRowCol
                if (this.panel?.layout[row][col].name === null) {
                    // New chart form
                    this.chartAddForm = await this.$fetch('/monitoring/charts/chart-edit/chart-add-form', this.selectedPanelIdRowCol)
                } else {
                    // Existing chart form handled by inner component
                }
            },
            immediate: true,
        },
    },

    methods: {
        chartEditRoute,
        async init (params: RouteParams) {
            await this.loadPanel(params.panelId!)
        },
        async loadPanel (id?: string) {
            this.panel = await this.$fetch<ChartPanel>('/monitoring/charts/panel-edit/form-and-layout', { panelId: id || this.panelId })
            await this.$nextTick()
            this.titleBreadcrumbs.setTitle('Edit panel: ' + this.panel?.form.values?.name)
        },
        getChartSelectorClasses (row: number, col: number, chart: Chart): Record<string, boolean> {
            let btnColor = 'btn-secondary'
            if (this.draggedChart?.chart && this.draggedChart.chart !== chart) {
                btnColor = 'btn-success'
            } else if (row === this.selectedChart?.row && col === this.selectedChart?.col) {
                btnColor = 'btn-primary'
            }

            return {
                [btnColor]: true,
                'font-italic': chart.name === null,
            }
        },
        async onPanelSave (params: Values) {
            if (!this.panel || !this.panelId) {
                return
            }
            params.panelId = this.panelId
            await this.$processFormResponse(this.$fetch('/monitoring/charts/panel-edit/save', params), this.panel.form)
            await this.loadPanel()
        },
        async onPanelDelete () {
            if (!confirm('Really delete this panel?')) {
                return
            }

            return this.$fetch('/monitoring/charts/panel-edit/delete', { panelId: this.panelId }).then(() => {
                this.$router.push({ name: 'panel-list' })
            })
        },
        async onChartAdd (params: ChartAddFormValues) {
            await this.$processFormResponse(this.$fetch('/monitoring/charts/chart-edit/chart-add', params), this.chartAddForm)
            await this.loadPanel()
            await this.$router.push(chartEditRoute(this.panelId, params.row, params.col, 0))
        },
        async onChartDelete () {
            this.selectedChart = undefined
            await this.loadPanel()
        },
        onDragStart (e: DragEvent, row: number, col: number, chart: Chart) {
            this.draggedChart = { chart, row, col }
            // force some browsers enable drag'n'drop
            e.dataTransfer!.setData('text/plain', '')
            e.dataTransfer!.dropEffect = 'move'
        },
        async onDrop (targetRow: number, targetCol: number, chart: Chart) {
            if (!this.draggedChart || chart === this.draggedChart.chart) {
                return
            }
            this.inProcess = true
            this.selectedChart = undefined
            await this.$fetch('/monitoring/charts/chart-switch', {
                panelId: this.panelId,
                row: this.draggedChart.row,
                col: this.draggedChart.col,
                targetRow: targetRow,
                targetCol: targetCol,
            })
            await this.loadPanel().finally(() => this.inProcess = false)
        },
        onDragEnd () {
            this.draggedChart = undefined
        },
    },
})
</script>
