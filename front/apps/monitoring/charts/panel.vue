<template>
    <Card width="wide">
        <table
            v-if="panel"
            class="charts-panel"
        >
            <tr v-for="(cols, row) in panel.layout">
                <td
                    v-for="(panel<PERSON>hart, col) in cols"
                    :style="{width: Math.round(100 / cols.length * panelChart.col_span) + '%'}"
                    :rowspan="panelChart.row_span || 1"
                    :colspan="panelChart.col_span || 1"
                >
                    <div
                        :id="`highchart-${row}-${col}`"
                        class="charts-chart"
                        :style="{height: panelChart.autoHeight + 'px', width: '100%'}"
                    />
                </td>
            </tr>
        </table>
    </Card>
</template>

<script lang="ts">

import { HighchartsLoader } from '@/components'
import { defineComponent } from 'vue'
import { NextWithReload, Values } from '@/types'
import { Options, Chart, XAxisOptions, SeriesLineOptions, AxisSetExtremesEventObject } from 'highcharts'
import { useTitleBreadcrumbs } from '@/utils/title-breadcrumbs.ts'
import { useTheme } from '@/utils/theme.ts'
import { Card } from '@/widgets'

/** @see \app\back\modules\monitoring\charts\ChartPanelLayout::getChartsLayoutConfig() */
interface ChartDrawSimpleConfig {
    row_span: number
    col_span: number
    height: number
    refresh_period: number
    autoHeight: number
    loadRange: number
}

interface ChartDrawConfig extends ChartDrawSimpleConfig {
    chartConfig: Options
}

interface PanelResponse {
    name: string
    layout: ChartDrawSimpleConfig[][]
}

export default defineComponent({
    components: { Card },
    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(vm => {
            return vm.reload(to.params)
        })
    },
    props: {
        panelId: {
            type: String,
            required: true,
        },
    },
    setup() {
        return {
            titleBreadcrumbs: useTitleBreadcrumbs(),
            theme: useTheme(),
        }
    },
    data () {
        return {
            panel: {} as PanelResponse,
            timers: {} as Record<string, number>,
        }
    },
    beforeUnmount () {
        this.clearIntervals()
    },
    methods: {
        async reload ({ panelId }: Values) {
            const panelWithConfigs = await this.$fetch('/monitoring/charts/panel/view/' + panelId);

            this.titleBreadcrumbs.setTitle(panelWithConfigs.name)
            this.clearIntervals()

            this.panel = {
                name: panelWithConfigs.name,
                layout: [],
            }

            for (const [row, cols] of panelWithConfigs.layout.entries()) {
                this.panel.layout[row] = []
                for (const [col, panelChart] of cols.entries()) {
                    const simpleChartConfig = Object.assign({}, panelChart)
                    delete simpleChartConfig.chartConfig

                    this.panel.layout[row][col] = simpleChartConfig
                    this.$nextTick(() => {
                        return this.draw(panelId as number, row, col, panelChart)
                    })
                }
            }
        },

        async draw (panelId: number, row: number, col: number, panelChart: ChartDrawConfig) {
            const conf = panelChart.chartConfig

            const plotBands = (chart: Chart) => {
                if (chart.series[0] === undefined) {
                    return
                }

                const data = chart.series[0].data

                if (data === null || data[0] === undefined) {
                    return
                }

                const day = 24 * 60 * 60 * 1000
                const edges = []
                const max = Math.floor(data[data.length - 1].x / (3600 * 1000)) * 3600 * 1000
                for (let i = max; i >= data[0].x; i -= day) {
                    edges.unshift(i)
                }

                if (edges.length % 2 !== 0) {
                    edges.unshift(edges[0] - day)
                }

                const bandsCount = edges.length / 2

                chart.xAxis[0].removePlotBand('days-plot-band')
                for (let i = 0; i < bandsCount; i++) {
                    chart.xAxis[0].addPlotBand({
                        id: 'days-plot-band',
                        from: edges.shift(),
                        to: edges.shift(),
                        color: this.theme.isDarkTheme.value ? '#444' : '#EEE',
                    })
                }
            }

            const plotCurrentLine = (chart: Chart) => {
                if (chart.series.length > 2) {
                    return
                }

                const data = chart.series[0].data

                chart.yAxis[0].removePlotLine('current_value')
                chart.yAxis[0].addPlotLine({
                    id: 'current_value',
                    color: '#666',
                    dashStyle: 'Dash',
                    width: 1,
                    value: data[data.length - 1].y,
                    zIndex: 5,
                })
            }

            const loadData = async (from: number, to: number) => {
                return this.$fetch('/monitoring/charts/panel/chart-data', { panelId, col, row, from, to })
            }

            const refresh = (chart: Chart) => {
                const xAxis = chart.xAxis[0]

                const { min, max } = xAxis.getExtremes()
                const range = max - min

                const to = (new Date()).valueOf()
                const from = (new Date(to - range)).valueOf()

                xAxis.setExtremes(from, to)
            }

            const xAxis = conf.xAxis as XAxisOptions[]
            xAxis[0].events = {
                afterSetExtremes (event: AxisSetExtremesEventObject) {
                    const chart = this.chart

                    loadData(Math.round(event.min), Math.round(event.max)).then(data => {
                        data.forEach(function (serieData: number[][], i: number) {
                            chart.series[i].setData(serieData)
                        })

                        plotCurrentLine(chart)
                        plotBands(chart)
                    })
                },
            }

            const toInitial = (new Date()).valueOf()

            if (conf.navigator?.enabled) {
                const fromInitial = (new Date(toInitial - panelChart.loadRange)).valueOf()
                const navigatorData = await loadData(fromInitial, toInitial);
                (conf.navigator.series as SeriesLineOptions[])[0].data = navigatorData[0]
            }

            const chart = (await HighchartsLoader()).default.stockChart('highchart-' + row + '-' + col, conf)

            if (xAxis[0].range) {
                chart.xAxis[0].setExtremes(toInitial - (xAxis[0].range), toInitial)
            }

            if (panelChart.refresh_period > 0) {
                const timerKey = `${panelId}-${row}-${col}`
                if (this.timers[timerKey]) {
                    clearInterval(this.timers[timerKey])
                }

                this.timers[timerKey] = setInterval(() => refresh(chart), panelChart.refresh_period * 60 * 1000) as unknown as number
            }
        },

        clearIntervals () {
            Object.values(this.timers).forEach(clearInterval)
            this.timers = {}
        },
    },
})
</script>
