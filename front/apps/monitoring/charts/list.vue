<template>
    <Card>
        <RichTable
            v-bind="richTable"
            showPagination
            @reload="reload"
        >
            <template #afterTitle="{refreshCallback}">
                <Popover
                    title="Create new chart"
                    position="right"
                    @open="onCreateOpen"
                >
                    <button
                        type="button"
                        class="btn btn-sm btn-success"
                    >
                        <Icona name="icn-plus" /> New chart
                    </button>
                    <template #content>
                        <FormGrid
                            v-bind="createForm"
                            @change="createForm.values = $event"
                            @submit="onCreateSubmit($event, refreshCallback)"
                        />
                    </template>
                </Popover>
            </template>
            <template #name="{row}: {row: ChartTableRow}">
                {{ row.name }}
                <br>
                <em>{{ row.createdBy }}</em>
            </template>
            <template #layout="{row}">
                <div>
                    <table class="table table-bordered">
                        <tbody>
                            <tr v-for="cols in row.layout">
                                <td
                                    v-for="chart in cols"
                                    :style="{width: Math.floor(100 / cols.length * chart.col_span) + '%'}"
                                    :rowspan="Math.max(chart.row_span, 1)"
                                    :colspan="Math.max(chart.col_span, 1)"
                                >
                                    {{ chart.name }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </template>
            <template #actions="{row}">
                <div class="text-nowrap">
                    <span
                        class="p-2 pointer"
                        @click="onFavoriteSet(row.id, !row.favorite)"
                    >
                        <Icona
                            :name="row.favorite ? 'icn-star-filled' : 'icn-star'"
                            :class="row.favorite ? 'text-warning' : ''"
                        />
                    </span>

                    <router-link
                        class="btn btn-sm btn-primary"
                        :to="{name: 'panel', params: {panelId: row.id}}"
                    >
                        <Icona name="icn-eye" /> View
                    </router-link>

                    <router-link
                        class="btn btn-sm btn-warning ms-1"
                        :to="{name: 'panel-edit', params: {panelId: row.id}}"
                    >
                        <Icona name="icn-pencil" /> Edit
                    </router-link>
                </div>
            </template>
        </RichTable>
    </Card>
</template>

<script lang="ts">

import { RichTable, FormGrid, Popover, Icona } from '@/components'
import { defineComponent } from 'vue'
import { FormGridType, NextWithReload, RichTableType, Values } from '@/types'
import { Card } from '@/widgets'

interface ChartTableRow {
    name: string
    createdBy: string
}

export default defineComponent({
    components: {
        Card,
        Icona,
        RichTable,
        FormGrid,
        Popover,
    },
    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(vm => vm.reload(vm.$decodeParams(to.query)))
    },
    data () {
        return {
            richTable: {} as RichTableType,
            createForm: {} as FormGridType,
            backUrl: '/monitoring/charts/panel',
        }
    },
    methods: {
        async reload (params: Values) {
            await this.$processRichTableResponse(this.$fetch(`${this.backUrl}/list`, params), this.richTable)
        },
        async onCreateOpen () {
            this.createForm = await this.$fetch(`${this.backUrl}/add-form`)
        },
        async onCreateSubmit (params: Values, refreshCallBack: () => void) {
            await this.$processFormResponse(this.$fetch(`${this.backUrl}/add`, params), this.createForm)
            refreshCallBack()
        },
        async onFavoriteSet (panelId: number, favorite: boolean) {
            await this.$fetch(`${this.backUrl}/set-favorite`, { panelId, favorite })
            if (this.richTable.form?.values) {
                await this.reload(this.richTable.form.values)
            }
        },
    },
})
</script>
