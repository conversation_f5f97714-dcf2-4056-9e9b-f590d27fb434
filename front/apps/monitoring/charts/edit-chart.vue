<template>
    <Card disableBreadcrumbs>
        <h3>
            Chart
            <button
                type="button"
                class="btn btn-danger btn-sm"
                @click="onChartDelete"
            >
                <Icona name="icn-delete" /> Delete
            </button>
        </h3>
        <FormGrid
            v-bind="chartForm"
            @change="onChartChange"
        />
        <h3>Series</h3>
        <TabsRouter :tabs="tabs" tabIdRouteParamName="serieIndex">
            <template #default="{ tab, tabId }">
                <template v-if="tabId === undefined">
                    <FormGrid
                        v-bind="addSerieForm"
                        @change="addSerieForm.values = $event"
                        @submit="onChartSerieAdd"
                    />
                </template>
                <template v-else-if="tab && 'serieForm' in tab">
                    <FormGrid
                        v-bind="tab.serieForm"
                        @change="onChartSerieChange"
                    />
                    <div class="card-body row px-0">
                        <div class="col-md-6">
                            <FormParams
                                :errors="tab.errors"
                                :disabled="inProcessFinal"
                                :formValues="tab.flatFormValues!"
                                :inputsSets="tab.inputsSets!"
                                @paramClick="onParamClick"
                            />
                        </div>
                        <div class="col-md-6">
                            <FormList
                                ref="list"
                                class="mt-1"
                                :disabled="inProcessFinal"
                                :formBlocks="[tab.formValuesBlock!]"
                                :inputsByName="tab.inputsByName!"
                                @change="onChange"
                                @changeOperator="onChangeOperator"
                                @delete="onDelete"
                            />
                        </div>
                    </div>
                    <div class="input-group">
                        <label class="input-group-text">Metric</label>
                        <Dropdown
                            :enabled="!inProcessFinal"
                            :isInvalid="'metric' in (tab.errors || {})"
                            :value="tab.metric"
                            :allowToggleAll="false"
                            v-bind="tab.metricDropdown"
                            @input="onMetricChanged($event as string | undefined)"
                        />
                        <button
                            type="button"
                            class="btn btn-danger"
                            :disabled="inProcessFinal"
                            @click="onChartSerieDelete()"
                        >
                            <Icona name="icn-delete" /> Delete serie
                        </button>
                        <button
                            v-if="showSqlButton && !hasError"
                            type="button"
                            class="btn btn-outline-warning"
                            :disabled="inProcessFinal"
                            @click="toggleSql"
                        >
                            <Icona name="icn-database" /> SQL
                        </button>
                        <button
                            type="button"
                            class="btn btn-success"
                            :disabled="inProcessFinal"
                            @click="onChartSerieClone()"
                        >
                            <Icona name="icn-copy" /> Clone serie
                        </button>
                    </div>
                </template>
            </template>
        </TabsRouter>
    </Card>
    <br>
    <Card v-if="tabs.length > 1" disableBreadcrumbs>
        <template v-if="sql && !hasError">
            <h3>SQL</h3>
            <pre
                class="p-3"
                style="white-space: pre-line;"
            >{{ sql }}</pre>
        </template>
        <h3>Preview</h3>
        <div
            id="panels-chart-highcharts-preview"
            style="min-height: 500px;"
        />
    </Card>
</template>

<script lang="ts">

import { defineComponent, PropType } from 'vue'
import { FormList, FormParams, FormGrid, HighchartsLoader, Dropdown, Icona, TabsRouter } from '@/components'
import { $emptyFormValuesBlock, $emptyListParamWithKey, $fromValuesToUrlParams, $toggleFormValue } from '@/utils/form-list-utils'
import { FormGridType, FormBlock, FormInput, FormInputsSet, FormValue, FormValueIndex, Values, Value, Errors, Item, ItemGroup, Tab } from '@/types'
import { PanelIdRowCol } from './edit.vue'
import { Card } from '@/widgets'

type Serie = {
    name: string
    className: string
    order: number
    source: string
    nulls_strategy: number
}

interface MetricDropdown {
    items: Item<string>[]
    groups: ItemGroup[]
    multiple: boolean
}

interface SerieSourceConfig {
    metric: string
    filters: [string, Value, string][]
}

interface SerieTab extends Tab {
    serieForm: FormGridType<Serie>
    metricDropdown: MetricDropdown
    inputsSets: FormInputsSet[]
    inputsByName: Record<string, FormInput>
    metric?: string
    flatFormValues: FormValue[]
    formValuesBlock: FormBlock
    errors?: Errors
}

interface SerieNewTab extends Tab {
    route: {
        name: string
        params: {
            panelId: string
            rowCol: string
            serieIndex: string | undefined
        }
    }
}

type ChartSeriesResponse = {
    series: {
        serie: FormGridType<Serie>
        sourceConfig: SerieSourceConfig
        inputsSets: FormInputsSet[]
        metricDropdown: MetricDropdown
    }[]
}

type SerieTabOrNewSerieTab = SerieTab | SerieNewTab

export function chartEditRoute(panelId: string, row: number, col: number, serieIndex: number | undefined) {
    return {
        name: 'panel-edit',
        params: {
            panelId: String(panelId),
            rowCol: row + '-' + col,
            serieIndex: serieIndex !== undefined ? String(serieIndex) : undefined,
        },
    }
}

function flatFormFiltersToBlock (formFilters: FormValue[], inputsSets: FormInputsSet[]): FormBlock {
    const inputsNamesToSetIndex: Record<string, number> = {}
    inputsSets.forEach((set, setIndex) => set.inputs.forEach(input => {
        inputsNamesToSetIndex[input.name] = setIndex
    }))

    const block = $emptyFormValuesBlock(inputsSets)
    formFilters.forEach(f => {
        block.blockSets[inputsNamesToSetIndex[f.name]].values.push(f)
    })
    return block
}

function inputsByName (inputsSets: FormInputsSet[]): Record<string, FormInput> {
    const inputs: Record<string, FormInput> = {}
    inputsSets.forEach(set => set.inputs.forEach(input => {
        inputs[input.name] = input
    }))
    return inputs
}

export default defineComponent({
    components: {
        Card,
        Dropdown,
        FormGrid,
        FormList,
        FormParams,
        Icona,
        TabsRouter,
    },
    props: {
        panelRowCol: {
            type: Object as PropType<PanelIdRowCol>,
            required: true,
        },
        serieIndex: {
            type: String,
            default: undefined,
        },
        inProcess: {
            type: Boolean,
            required: true,
        },
        showSqlButton: {
            type: Boolean,
            required: true,
        },
    },
    emits: {
        'chartDelete': null,
        'panelUpdate': null,
    },
    data () {
        return {
            chartForm: {} as FormGridType,
            addSerieForm: {} as FormGridType,
            tabs: [] as SerieTabOrNewSerieTab[],
            hasError: false,
            sql: undefined as string | undefined,
            inProcessChart: false,
        }
    },

    computed: {
        selectedChartSerieIndex() {
            return {
                ...this.panelRowCol,
                serieIndex: this.serieIndex!,
            }
        },
        inProcessFinal () {
            return this.inProcess || this.inProcessChart
        },
        activeSerieTab() {
            if (this.serieIndex === undefined) {
                return
            }

            return this.tabs[Number(this.serieIndex)] as SerieTab
        },
    },
    watch: {
        panelRowCol: {
            async handler() {
                await this.loadAll()
            },
            immediate: true,
        },
    },

    mounted () {
        this.resetTabs()
    },

    methods: {
        async loadAll() {
            await this.loadChartForm()
            await this.loadSeries()
            await this.loadAndDrawHighChart()
            this.addSerieForm = await this.$fetch('/monitoring/charts/chart-edit/serie-add-form', this.panelRowCol)
        },
        async onChartDelete() {
            if (!confirm('Really delete chart?')) {
                return
            }

            await this.$fetch('/monitoring/charts/chart-edit/chart-delete', this.panelRowCol)

            this.$emit('chartDelete')
        },
        async loadChartForm() {
            try {
                this.chartForm = await this.$fetch('/monitoring/charts/chart-edit/chart-edit-form', this.panelRowCol)
            } catch (e) {
                this.$notify({
                    type: 'error',
                    message: 'Chart form loading failed! Danger! Can lead to series config loss! Do not edit',
                })
                return
            }
        },
        async loadSeries () {
            try {
                const r = await this.$fetch<ChartSeriesResponse>('/monitoring/charts/chart-edit/chart-series-forms', this.panelRowCol)
                this.resetTabs()
                const { panelId, row, col } = this.panelRowCol
                r.series.forEach((serie, i) => {
                    const flatFormValues = serie.sourceConfig.filters.map(([name, value, operator]: [string, Value, string]) => ({
                        ...$emptyListParamWithKey(),
                        name,
                        value,
                        operator,
                    }))

                    this.tabs.splice(i, 0, {
                        route: chartEditRoute(panelId, row, col, i),
                        title: serie.serie.values?.name || 'No name serie',
                        serieForm: serie.serie,
                        metricDropdown: serie.metricDropdown,
                        inputsSets: serie.inputsSets,
                        inputsByName: inputsByName(serie.inputsSets),
                        metric: serie.sourceConfig.metric,
                        flatFormValues,
                        formValuesBlock: flatFormFiltersToBlock(flatFormValues, serie.inputsSets),
                    })
                })
                await this.$router.push(chartEditRoute(panelId, row, col, Math.max(r.series.length - 1, 0)))
            } catch (e) {
                this.$notify({
                    type: 'error',
                    message: 'Chart series loading failed! Danger! Can lead to series config loss! Do not edit',
                })
            }
        },
        async onChartChange (chartConfig: Values) {
            const params = Object.assign({ config: chartConfig }, this.panelRowCol)
            delete params.config.series
            await this.$processFormResponse(this.$fetch('/monitoring/charts/chart-edit/chart-save', params), this.chartForm)
            this.$emit('panelUpdate')
            await this.loadChartForm()
            await this.loadAndDrawHighChart()
        },
        async toggleSql () {
            if (this.sql) {
                this.sql = undefined
                return
            }

            await this.loadSql()
        },
        async loadSql () {
            this.sql = undefined
            const data = await this.$fetch<{ sql: string }>('/monitoring/charts/chart-edit/serie-sql', this.selectedChartSerieIndex)
            this.sql = data.sql
        },
        async loadAndDrawHighChart () {
            if (this.tabs.length <= 1) {
                // Do not load chart if there is no series
                return
            }
            const conf = await this.$fetch('/monitoring/charts/chart-edit/high-charts-config', this.panelRowCol)
            this.destroyHighchart()
            const { default: Highcharts } = await HighchartsLoader()
            this.$options.highchart = Highcharts.stockChart('panels-chart-highcharts-preview', conf)
            const series = await this.loadChartData(conf)
            series.forEach((serieData: unknown, i: number) => {
                this.$options.highchart.series[i].setData(serieData, false)
            })
            this.$options.highchart.redraw()
            this.hasError = false
        },
        async loadChartData (conf: { xAxis: Array<{ range: number }> }) {
            const to = (new Date()).valueOf() - 60 * 1000
            const from = (new Date(to - conf.xAxis[0].range)).valueOf()
            if (this.sql !== undefined) {
                await this.loadSql()
            }
            return this.$fetch('/monitoring/charts/panel/chart-data', Object.assign({}, this.panelRowCol, { from, to }))
        },
        resetTabs () {
            this.tabs = [
                {
                    title: 'Add new serie',
                    route: chartEditRoute(this.panelRowCol.panelId, this.panelRowCol.row, this.panelRowCol.col, undefined),
                },
            ]
        },
        destroyHighchart () {
            if (this.$options.highchart) {
                this.$options.highchart.destroy()
            }
        },
        async onChartSerieAdd (source: Values) {
            const params = Object.assign(source, this.panelRowCol)
            await this.$processFormResponse(this.$fetch('/monitoring/charts/chart-edit/serie-add', params), this.addSerieForm)
            await this.loadChartForm()
            await this.loadSeries()
            await this.loadAndDrawHighChart()
        },
        async onChartSerieDelete () {
            if (!confirm('Really delete serie?')) {
                return
            }

            await this.$fetch('/monitoring/charts/chart-edit/serie-delete', this.selectedChartSerieIndex)
            await this.loadSeries()
            await this.loadAndDrawHighChart()
        },
        async onChartSerieClone () {
            await this.$fetch('/monitoring/charts/chart-edit/serie-clone', this.selectedChartSerieIndex)
            await this.loadSeries()
            await this.loadAndDrawHighChart()
        },
        async onChartSerieChange (serie: Serie) {
            const params = Object.assign({ config: serie }, this.selectedChartSerieIndex)
            await this.$processFormResponse<Serie>(this.$fetch('/monitoring/charts/chart-edit/serie-save', params), this.activeSerieTab!.serieForm)
            await this.loadSeries()
            await this.loadAndDrawHighChart()
        },
        onParamClick (input: FormInput) {
            const setIndex = this.activeSerieTab!.inputsSets.findIndex(s => s.inputs.some(f => f.name === input.name))
            $toggleFormValue(input, this.activeSerieTab!.formValuesBlock.blockSets[setIndex])
        },
        onChange ({ valueIndex, value }: { valueIndex: FormValueIndex, value: Value }): void {
            this.activeSerieTab!.formValuesBlock!.blockSets[valueIndex.setIndex].values[valueIndex.valueIndex].value = value
            this.onValuesChanged()
        },
        onChangeOperator ({ valueIndex, operator }: { valueIndex: FormValueIndex, operator: string }): void {
            this.activeSerieTab!.formValuesBlock!.blockSets[valueIndex.setIndex].values[valueIndex.valueIndex].operator = operator
            this.onValuesChanged()
        },
        onDelete (valueIndex: FormValueIndex): void {
            this.activeSerieTab!.formValuesBlock!.blockSets[valueIndex.setIndex].values.splice(valueIndex.valueIndex, 1)
            this.onValuesChanged()
        },
        onMetricChanged (value: string | undefined): void {
            this.activeSerieTab!.metric = value
            this.onValuesChanged()
        },
        onValuesChanged (): void {
            const curTab = this.activeSerieTab!
            curTab.flatFormValues = curTab.formValuesBlock!.blockSets.map(s => s.values).flat()
            const params = {
                ...this.selectedChartSerieIndex,
                chartConfig: { ...$fromValuesToUrlParams(curTab.flatFormValues), ...{ metric: curTab.metric } },
            }

            interface ErrorResponse {
                status: number
                json(): Promise<{ errors?: Errors }>
            }

            this.inProcessChart = true
            this.$fetch('/monitoring/charts/chart-edit/serie-source-save', params).then(() => {
                delete curTab.errors
                curTab.flatFormValues!.forEach(param => delete param.error)
                return this.loadAndDrawHighChart()
            })
                .catch((resp: ErrorResponse) => {
                    this.hasError = true
                    if (resp.status !== 422) {
                        return
                    }

                    return resp.json().then(data => {
                        if (!('errors' in data)) {
                            return
                        }

                        curTab.errors = data.errors
                        curTab.flatFormValues!.forEach((param, index) => {
                            const key = `f${index}e`

                            if (data.errors && key in data.errors) {
                                param.error = data.errors[key]
                            } else if (data.errors && param.name in data.errors) {
                                param.error = data.errors[param.name]
                            } else {
                                delete param.error
                            }
                        })
                    })
                })
                .then(() => this.inProcessChart = false)
        },
    },
})
</script>
