<template>
    <Card :disable-help="true">
        <template #afterHelp>
            <button
                v-if="method"
                type="button"
                title="Method details"
                class="ms-2 btn btn-primary"
                @click="infoPopupOpened = true"
            >
                <Icona name="icn-question" size="lg" />
            </button>
        </template>

        <FormGrid
            v-bind="filterForm"
            @change="onApiParamsChange"
        />

        <Popup
            title="Info"
            width="large"
            :opened="infoPopupOpened"
            @close="infoPopupOpened = false"
        >
            <EntityTable
                v-bind="infoTable"
            >
                <template #operators="{row}: {row: {operators: string[]}}">
                    <span
                        v-for="operator in row.operators"
                        class="badge bg-secondary m-1"
                    >
                        {{ operator }}
                    </span>
                </template>
            </EntityTable>
        </Popup>
    </Card>
    <template v-if="filterForm?.values?.method">
        <div class="container mt-2">
            <div class="card">
                <div class="card-body">
                    <Request
                        v-if="formValuesBlock.ready"
                        :apiParams="filterForm.values"
                        :focusOnFormValue="focusOnFormValue"
                        :formBlock="formValuesBlock"
                        :inputsSets="inputsSets"
                        @response="onResponse"
                        @onParamClick="onParamClick"
                        @onParamDelete="onParamDelete"
                        @onParamChange="onParamChange"
                        @onParamChangeOperator="onParamChangeOperator"
                        @onReset="onReset"
                    />
                </div>
            </div>
        </div>
        <div
            v-if="response.code"
            class="container-fluid mt-2"
        >
            <div
                class="container-fluid pt-4"
            >
                <div class="card">
                    <Response
                        class="card-body"
                        :response="response"
                        :showLimit="showLimit"
                    />
                </div>
            </div>
        </div>
    </template>
</template>

<script lang="ts">
import { Card } from '@/widgets'
import { FormGrid, Popup, EntityTable, Icona } from '@/components'
import { defineComponent } from 'vue'
import { FormGridType, NextWithReload, Value, Values, TableType, NotifyMessage, FormValue, FormInputsSet, FormInput, FormValueIndex, FormBlock } from '@/types'
import { RouteParamsRaw } from 'vue-router'
import Request from './request.vue'
import Response, { ApiResponse } from './response.vue'
import { useTitleBreadcrumbs } from '@/utils/title-breadcrumbs.ts'
import { $emptyFormValuesBlock, $toggleFormValue } from '@/utils/form-list-utils.ts'

export interface ApiParams {
    client?: string | null
    type?: string | null
    method?: string | null
}

interface FormValuesBlockWithReadyCondition extends FormBlock {
    ready?: boolean // used in watcher to not trigger preset and history replace handling while this.reload()
}

export default defineComponent({
    components: {
        Icona,
        Request,
        Response,
        Card,
        FormGrid,
        Popup,
        EntityTable,
    },

    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(async vm => {
            await vm.reload(to.params)
            await vm.init(vm.$decodeParams(to.query))
        })
    },

    async beforeRouteUpdate (to, from) {
        if (to.path !== from.path) {
            await this.reload(to.params)
            this.init(this.$decodeParams(to.query))
        }
    },

    props: {
        // from route
        client: {
            type: String,
            default: null,
        },
        type: {
            type: String,
            default: null,
        },
        method: {
            type: String,
            default: null,
        },
    },
    setup () {
        return {
            titleBreadcrumbs: useTitleBreadcrumbs(),
        }
    },
    data () {
        return {
            filterForm: {} as FormGridType<ApiParams>,
            focusOnFormValue: undefined as FormValue | undefined,
            formValuesBlock: {blockSets: []} as FormValuesBlockWithReadyCondition,
            inputsSets: [] as FormInputsSet[],
            response: {
                code: 0,
                text: '',
                rows: 0,
                size: 0,
                type: '',
                data: [],
            } as ApiResponse,
            infoTable: {
                columns: [],
                data: [],
            } as TableType,
            infoPopupOpened: false,
            showLimit: 1000,
        }
    },
    computed: {
        inputNamesToSetIndex () {
            const valuesNamesToSetIndex: Record<string, number> = {}
            this.inputsSets.forEach((set, setIndex) => set.inputs.forEach(input => {
                valuesNamesToSetIndex[input.name] = setIndex
            }))
            return valuesNamesToSetIndex
        },
        urlData (): Record<string, string> {
            return Object.fromEntries(this.formValuesBlock.blockSets.map(s => s.values).flat().map(f => {
                let values = f.value

                if (values === null || values === undefined) {
                    values = ''
                }

                let name = f.name
                if ((this.filterForm.values as ApiParams).type === 'get') {
                    name += '[' + f.operator + ']'
                }

                return [name, values]
            })) as Record<string, string>
        },
    },

    watch: {
        formValuesBlock: {
            deep: true,
            flush: 'post',
            handler (to: FormValuesBlockWithReadyCondition, from: FormValuesBlockWithReadyCondition) {
                if (from.ready && to.ready) {
                    this.$historyReplaceParams(this.urlData, false)
                    this.saveAutoPreset(this.filterForm?.values as Values)
                }
            },
        },
    },

    methods: {
        async reload (params: Values) {
            Object.assign(this.formValuesBlock, {ready: false, sets: []})
            const form = await this.$fetch('/api/client/filter', params)
            const path = ['api', form.values.client, form.values.type, form.values.method].filter(p => p).join(' / ');
            this.titleBreadcrumbs.setTitle(path)
            this.filterForm = form

            if (this.filterForm.values?.method) {
                const resp = await this.$fetch('/api/client/form-fields', params)
                this.inputsSets = resp.inputsSets

                await this.$fetch('/api/client/info', params)
                    .then((richTable: {table: TableType}) => {
                        this.infoTable = richTable.table
                    })
            }
        },
        init (queryParams: Values) {
            let params: FormValue[] = []
            if (this.filterForm?.values?.type === 'get') {
                Object.entries(queryParams).forEach(([nameOperator, value]) => {
                    const result = nameOperator.match(/^([^[]+)\[([^\]]+)]$/)
                    if (result && result[1] in this.inputNamesToSetIndex) {
                        params.push({
                            name: result[1],
                            operator: result[2],
                            value: value,
                        } as FormValue)
                    }
                })
            } else {
                params = Object.entries(queryParams)
                    .filter(([name]) => name in this.inputNamesToSetIndex)
                    .map(([name, value]) => ({name, value} as FormValue))
            }

            this.formValuesBlock = this.flatFormValuesToBlock(params)
        },
        flatFormValuesToBlock (paramsObjects: FormValue[]): FormValuesBlockWithReadyCondition {
            const block: FormValuesBlockWithReadyCondition = $emptyFormValuesBlock(this.inputsSets)
            paramsObjects.forEach(param => {
                if (!(param.name in this.inputNamesToSetIndex)) {
                    // eslint-disable-next-line no-console
                    console.log(`Filter "${param.name}" unavailable and was removed!`)
                    return
                }
                const setIndex = this.inputNamesToSetIndex[param.name]
                block.blockSets[setIndex].values.push(param)
            })
            block.ready = true
            return block
        },
        onApiParamsChange (params: ApiParams) {
            if (!params.client || params.client !== this.client) {
                params.type = null
            }
            if (!params.type || params.type !== this.type) {
                params.method = null
            }
            this.resetResponse()
            this.$router.push({
                name: 'api-client',
                params: (params as RouteParamsRaw),
                query: this.getAutoPresetData(params),
            })
        },
        onResponse (response: ApiResponse | null) {
            if (response) {
                const l = response?.data ? response.data.length : 0
                if (l > this.showLimit) {
                    this.$notify({message: `Only ${this.showLimit} from ${l} rows showed`, type: "warning"} as NotifyMessage)
                    response.data = response.data.slice(0, this.showLimit)
                }
                this.response = response
            } else {
                this.resetResponse()
            }
        },
        onParamClick (input: FormInput) {
            const setIndex = this.inputNamesToSetIndex[input.name]
            this.focusOnFormValue = $toggleFormValue(input, this.formValuesBlock.blockSets[setIndex], () => {
                if ((input.operators || []).length <= 1 && this.formValuesBlock.blockSets.map(s => s.values).flat().some(f => f.name === input.name)) {
                    this.$notify({ message: 'Same filter already exists', type: 'error' })
                    return false
                }
                return true
            })
        },
        onParamDelete (valueIndex: FormValueIndex) {
            this.formValuesBlock.blockSets[valueIndex.setIndex].values.splice(valueIndex.valueIndex, 1)
        },
        onParamChange ({ valueIndex, value }: { valueIndex: FormValueIndex, value: Value }) {
            this.formValuesBlock.blockSets[valueIndex.setIndex].values[valueIndex.valueIndex].value = value
        },
        onParamChangeOperator ({ valueIndex, operator }: { valueIndex: FormValueIndex, operator: string }) {
            this.formValuesBlock.blockSets[valueIndex.setIndex].values[valueIndex.valueIndex].operator = operator
        },
        onReset () {
            this.formValuesBlock = $emptyFormValuesBlock(this.inputsSets)
            this.formValuesBlock.ready = true
        },
        resetResponse (code?: number) {
            if (code === undefined) {
                code = 0
            }

            this.response = {
                code,
                text: '',
                rows: 0,
                size: 0,
                type: '',
                data: [],
            }
        },
        storageKey (params: ApiParams): string | undefined {
            if (params?.client && params?.type && params?.method) {
                return `api-client-${params.client}-${params.type}-${params.method}`
            }
        },
        getAutoPresetData (params: ApiParams): Record<string, string> | undefined {
            const storageKey = this.storageKey(params)
            return storageKey ? JSON.parse(localStorage.getItem(storageKey) || '{}') : {}
        },
        saveAutoPreset (params: ApiParams) {
            const storageKey = this.storageKey(params)
            if (storageKey) {
                localStorage.setItem(storageKey, JSON.stringify(this.urlData))
            }
        },
    },
})
</script>
