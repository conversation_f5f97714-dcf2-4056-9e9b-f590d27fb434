<template>
    <div class="row">
        <div class="col-md-6">
            <FormParams
                :disabled="busy"
                :formValues="flatFormValues"
                :inputsSets="inputsSets"
                @paramClick="$emit('onParamClick', $event)"
            />
        </div>
        <div v-if="formBlock" class="col-md-6">
            <FormList
                ref="list"
                class="mt-1"
                :disabled="busy"
                :formBlocks="[formBlock]"
                :inputsByName="inputsByName"
                :focusOnFormValue="focusOnFormValue"
                @submit="onSubmit"
                @change="$emit('onParamChange', $event)"
                @changeOperator="$emit('onParamChangeOperator', $event)"
                @delete="$emit('onParamDelete', $event)"
            />
        </div>
    </div>
    <div class="d-flex justify-content-end">
        <button
            type="button"
            class="btn btn-success me-1"
            style="text-transform: capitalize;"
            :disabled="busy"
            @click="onSubmit"
        >
            <Icona name="icn-list" />
            {{ apiParams.type }}
        </button>
        <button
            type="button"
            class="btn btn-secondary me-1"
            :disabled="busy"
            @click="onShowUrl"
        >
            <Icona name="icn-link" /> Show URL
        </button>
        <button
            type="button"
            class="btn btn-secondary me-1"
            :disabled="busy"
            @click="onShowSql"
        >
            <Icona name="icn-database" /> Show SQL
        </button>
        <button
            type="button"
            class="btn btn-secondary me-1"
            :disabled="busy"
            @click="$emit('onReset')"
        >
            <Icona name="icn-refresh" /> Reset
        </button>
    </div>
</template>

<script lang="ts">
import { FormParams, FormList, Icona } from '@/components'
import { defineComponent, PropType } from 'vue'
import { FormInput, FormInputsSet, FormValue, FormBlock } from '@/types'
import { ApiParams } from './api-client.vue'

export default defineComponent({
    components: {
        Icona,
        FormParams,
        FormList,
    },

    props: {
        apiParams: {
            type: Object as PropType<ApiParams>,
            required: true,
        },
        focusOnFormValue: {
            type: Object as PropType<FormValue | undefined>,
            default: undefined,
        },
        formBlock: {
            type: Object as PropType<FormBlock>,
            required: true,
        },
        inputsSets: {
            type: Array as PropType<FormInputsSet[]>,
            required: true,
        },
    },

    emits: [
        'response',
        'onParamClick',
        'onParamChange',
        'onParamChangeOperator',
        'onParamDelete',
        'onReset',
    ],

    data () {
        return {
            busy: false,
        }
    },

    computed: {
        flatFormValues (): FormValue[] {
            return this.formBlock.blockSets.map(s => s.values).flat()
        },
        inputsByName (): Record<string, FormInput> {
            return Object.fromEntries(this.inputsSets.map(set => set.inputs).flat().map(f => [f.name, f]))
        },
    },

    methods: {
        async getUrl (): Promise<string> {
            this.$emit('response', null)
            this.busy = true
            return this.$fetch(`/api/client/url?client=${this.apiParams.client}&type=${this.apiParams.type}&method=${this.apiParams.method}`, this.requestBody(), false).then((data: { url: string }) => {
                return data.url
            }).finally(() => {
                this.busy = false
            })
        },
        async onShowUrl () {
            const url = await this.getUrl()
            const fullUrl = url + '?' + new URLSearchParams(this.requestData()).toString()

            this.$emit('response', {
                code: 200,
                type: '',
                text: fullUrl,
                size: fullUrl.length,
                rows: 0,
            })
        },
        async onShowSql () {
            const url = await this.getUrl()
            this.busy = true
            fetch(url + '?sql_debug', {
                method: 'post',
                body: this.requestBody(),
            }).then(resp => resp.text()).then(sql => {
                this.$emit('response', {
                    code: 200,
                    type: '',
                    text: sql,
                    size: sql.length,
                    rows: 0,
                })
            }).finally(() => {
                this.busy = false
            })
        },
        async onSubmit () {
            const url = await this.getUrl()
            this.busy = true
            await fetch(url, {
                method: 'post',
                body: this.requestBody(),
                headers: {
                    Accept: 'text/csv, application/json',
                },
            }).then(resp => {
                const apiResponse = {
                    code: resp.status,
                    type: resp.headers.get('Content-Type') as string,
                }

                if (apiResponse.type.startsWith('text/csv')) {
                    resp.text().then(text => {
                        const csv = this.parseCSV(text)
                        this.$emit('response', Object.assign(apiResponse, {
                            text: '',
                            data: csv,
                            size: text.length,
                            rows: text.length === 0 ? 0 : csv.length - 1,
                        }))
                    })
                } else if (apiResponse.type.startsWith('application/json')) {
                    resp.json().then(json => {
                        const text = JSON.stringify(json, null, 2)
                        this.$emit('response', Object.assign(apiResponse, {
                            text,
                            data: [],
                            size: json.length,
                            rows: 'length' in json ? json.length : 0,
                        }))
                    })
                } else {
                    resp.text().then(text => {
                        this.$emit('response', Object.assign(apiResponse, {
                            text,
                            data: [],
                            size: text.length,
                            rows: 0,
                        }))
                    })
                }
            }).finally(() => {
                this.busy = false
            })
        },
        parseCSV (str: string) {
            // copy from https://stackoverflow.com/questions/1293147/javascript-code-to-parse-csv-data
            const arr: string[][] = []
            let quote = false
            for (let row = 0, col = 0, c = 0; c < str.length; c++) {
                const cc = str[c]
                const nc = str[c + 1]
                arr[row] = arr[row] || []
                arr[row][col] = arr[row][col] || ''

                if (cc === '"' && quote && nc === '"') {
                    arr[row][col] += cc
                    ++c
                    continue
                }
                if (cc === '"') {
                    quote = !quote
                    continue
                }
                if (cc === ';' && !quote) {
                    ++col
                    continue
                }
                if (cc === '\r' && nc === '\n' && !quote) {
                    ++row
                    col = 0
                    ++c
                    continue
                }
                if (cc === '\n' && !quote) {
                    ++row
                    col = 0
                    continue
                }
                if (cc === '\r' && !quote) {
                    ++row
                    col = 0
                    continue
                }

                arr[row][col] += cc
            }

            return arr
        },
        requestBody () {
            const formData = new FormData()
            // build object
            this.requestData().forEach(([name, value]) => {
                formData.append(name, value)
            })

            return formData
        },
        requestData () {
            return this.flatFormValues.reduce((carry: string[][], v) => {
                let name = v.name
                let values = v.value

                if (this.apiParams.type === 'get') {
                    name += '[' + v.operator + ']'
                }

                if (values === '' || values === null || values === undefined) {
                    return carry
                }

                if (typeof values === 'string' && this.isMultiOperator(v.operator as string)) {
                    values = values.split('\n')
                }

                if (Array.isArray(values) && values.length === 1 && !this.isMultiOperator(v.operator as string)) {
                    values = values[0]
                }

                if (Array.isArray(values)) {
                    values.forEach(v => {
                        carry.push([name + '[]', typeof v === 'string' ? v : v.toString()])
                    })
                } else {
                    carry.push([name, typeof values === 'string' ? values : values.toString()])
                }

                return carry
            }, [])
        },
        isMultiOperator (operator: string) {
            return ['IN', 'NOT IN', 'BETWEEN', 'PREFIX IN', 'PREFIX NOT IN'].includes(operator)
        },
    },
})
</script>
