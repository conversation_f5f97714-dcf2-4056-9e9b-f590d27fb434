<template>
    <Card width="wide">
        <template #afterHeader>
            <FormGrid
                class="ms-3"
                v-bind="cidForm"
                :enabled="!inProgress && !statInProgress"
                @change="onCidChange"
                @submit="onCidSubmit"
            >
                <template #cellTitle>
                    <span />
                </template>
            </FormGrid>
        </template>

        <div
            class="row cid-visualisation-container"
            :style="visGraphStyle"
        >
            <div class="col-sm-3 h-100">
                <Highlight
                    :sites="currentSitesTableData.filter(site => site.row_name !== sitesTotalRowName)"
                    :activeSites="activeSites"
                    @onSearchSubmit="onHighlightSearch"
                    @onToggleSite="onHighlightSites"
                    @onToggleSitesTable="toggleSitesTable"
                />
                <div
                    class="mt-2"
                    style="overflow-y: auto;"
                >
                    <SitesTable
                        :tableData="currentSitesTableData"
                        :sitesTotalRowName="sitesTotalRowName"
                    />
                </div>

                <div
                    class="mt-2"
                    style="overflow-y: auto; max-height: 30%;"
                >
                    <RequisitesTable
                        :table-data="requisitesData"
                    />
                </div>
            </div>
            <div class="col-sm-9 h-100 overflow-hidden">
                <li>
                    <button
                        class="btn btn-primary ms-2"
                        type="button"
                        title="Pause solving"
                        @click="togglePause"
                    >
                        <Icona :name="isPaused ? 'icn-play' : 'icn-pause'" />
                    </button>

                    <button
                        class="btn btn-primary ms-2"
                        type="button"
                        title="Toggle labels visibility when solving paused"
                        @click="onToggleLabelsVisibility"
                    >
                        <Icona :name="isLabelsVisible ? 'icn-eye' : 'icn-eye-off'" />
                    </button>

                    <button
                        class="btn btn-primary ms-2"
                        type="button"
                        title="Show centrality"
                        @click="onShowCentrality"
                    >
                        <Icona name="icn-circle-dot" />
                    </button>

                    <button
                        class="btn btn-primary ms-2"
                        type="button"
                        title="Highlight next valuable node"
                        @click="onNextNode"
                    >
                        <Icona name="icn-player-fast-forward" />
                    </button>

                    <button
                        v-show="currentCid !== false"
                        class="cid-number btn btn-selectable fw-bold ms-2"
                        :class="{'btn-success': currentCid, 'disabled btn-danger': !currentCid}"
                        @click="copy(String(currentCid))"
                    >
                        CID <span ref="copy">{{ `${currentCid || 'undefined'}` }}</span>
                    </button>

                    <button
                        v-show="currentCid !== false && currentType !== false"
                        class="btn btn-primary ms-2"
                        type="button"
                        title="NGR chart"
                        @click="openChartPopup(true)"
                    >
                        <Icona name="icn-chart" />
                    </button>
                    <button
                        class="btn btn-primary ms-2"
                        type="button"
                        title="Requisites last(IN/OUT) year"
                        :disabled="!currentCid || !cidForm || statInProgress"
                        @click="onHighlightOlds()"
                    >
                        <Icona
                            name="icn-hourglass"
                            :class="{'icona-spinner': statInProgress}"
                        />
                    </button>
                    <button
                        class="btn btn-primary ms-2"
                        type="button"
                        title="Open FAQ"
                        @click="openFaqPopup(true)"
                    >
                        <Icona name="icn-info" />
                    </button>

                    <button
                        class="btn btn-secondary ms-2"
                        type="button"
                        title="Reset default values"
                        @click="onResetValues"
                    >
                        <Icona name="icn-reset" />
                    </button>
                    <div class="btn-group ms-2">
                        <button
                            class="btn btn-secondary"
                            type="button"
                            title="Deposit lower than withdrawal"
                            @click="onToggleNodeHighlight(nodeTypes.USER_TOXIC)"
                        >
                            <Icona name="icn-toxic" />
                        </button>
                        <button
                            class="btn btn-secondary"
                            type="button"
                            title="Highlight face id"
                            @click="onToggleNodeHighlight(nodeTypes.FACE)"
                        >
                            <Icona name="icn-vcard" />
                        </button>
                    </div>
                </li>
                <div style="flex-grow: 1;">
                    <div class="position-absolute">
                        <span
                            v-for="(label, key) in domLabels"
                            :key="key"
                            :class="label.class"
                            :style="label.style"
                            v-html="label.html"
                        />
                    </div>
                    <div
                        ref="graph"
                        class="h-100"
                    />
                </div>
                <div style="height: 2rem;">
                    <template v-if="selectedNode">
                        <template v-if="selectedNode.data.t === nodeTypes.FACE && !isSourceClusterData(selectedNode.data)">
                            <div
                                v-if="faceDataLoadInProgress"
                                class="row text-center position-absolute"
                                style="bottom: 0"
                            >
                                Loading...
                            </div>
                            <div
                                v-else-if="faceImages.length"
                                ref="face-list"
                                class="text-center position-relative hor-img-list"
                                @scroll="loadNextVisibleFace"
                            >
                                <div
                                    v-for="img in faceImages"
                                    class="img-responsive img-thumbnail ps-1 pe-1 d-inline-block h-100"
                                >
                                    <a
                                        :href="(img.visible && img.url) ? img.url : ''"
                                        class="h-100 d-inline-block face-image"
                                        target="_blank"
                                    >
                                        <img
                                            :ref="`img-${img.url}`"
                                            :src="(img.visible && img.url) ? img.url : ''"
                                            class="img-not-loaded h-100 border rounded progress-bar progress-bar-striped bg-secondary"
                                            role="progressbar"
                                            aria-valuenow="100"
                                            :class="{'progress-bar-animated': img.visible}"
                                            @load="afterFaceImageLoading($event, true)"
                                            @error="afterFaceImageLoading($event, false)"
                                        >
                                    </a>
                                </div>
                            </div>
                        </template>

                        {{ selectedNodeBottomInfo.start }}
                        <button
                            v-if="selectedNodeBottomInfo.copyContent"
                            class="btn btn-link"
                            @click="copy(selectedNodeBottomInfo.copyContent)"
                        >
                            {{ selectedNodeBottomInfo.copyContent }}
                        </button>
                        {{ selectedNodeBottomInfo.end }}

                        <template v-if="selectedNode.data.asyncInfo">
                            <Icona
                                v-if="selectedNode.data.asyncInfoError"
                                name="icn-exclamation-triangle"
                                class="text-danger"
                                title="Unable to load info"
                            />
                            <Icona
                                v-else
                                name="icn-refresh"
                                class="icona-spinner"
                            />
                        </template>
                    </template>
                </div>
            </div>
        </div>

        <Popup
            title="FAQ"
            :opened="faqPopupOpened"
            @close="openFaqPopup(false)"
        >
            <dl class="row">
                <template v-for="{path, description} in imageDescriptions">
                    <dt
                        class="col-sm-4 my-1"
                        style="height: 2rem"
                    >
                        <img
                            style="height: 2rem"
                            :src="path"
                        >
                    </dt>

                    <dd class="col-sm-8 align-self-center">
                        {{ description }}
                    </dd>
                </template>
            </dl>
        </Popup>

        <Popup
            width="large"
            :opened="ngrChartPopupOpened || ngrChartLoadInProgress"
            @close="openChartPopup(false)"
        >
            <template v-if="ngrChartPopupOpened">
                <div id="ngr-chart" />
            </template>
            <template v-else-if="ngrChartLoadInProgress">
                <span>Loading...</span>
            </template>
        </Popup>
    </Card>
</template>
<script lang="ts">
/* quick adaptation to TypeScript todo refactoring */
import { FormGrid, Popup, HighchartsLoader, Icona } from '@/components'
import { Card } from '@/widgets'
import Viva, { NodePosition, Renderer, WebglImageNodeProgram } from 'vivagraphjs'
import Highlight from './cid-visualisation/highlight.vue'
import SitesTable, { SiteBrandRow } from './cid-visualisation/sites-table.vue'
import RequisitesTable, { RequisiteRow } from './cid-visualisation/requisites-table.vue'
import { LuminousGallery } from 'luminous-lightbox'
import createGraph, { Graph, Link, LinkId, Node, NodeId } from 'ngraph.graph'
import detectCommunities from 'ngraph.louvain'
import coarsen from 'ngraph.coarsen'
import { CSSProperties, defineComponent } from 'vue'
import { Values, FormGridType, WithReload } from '@/types'
import { LocationQuery } from 'vue-router'
import { copyToClipboard } from '@/utils/clipboard.ts'
import { useTheme } from '@/utils/theme.ts'


interface WithReloadCurrent extends WithReload {
    loadConfig (): Promise<null>
    processQueryParams (query: LocationQuery, hash: string): Promise<null>
}

type NextCurrent = (nextHandler: (vm: WithReloadCurrent) => void) => void

interface NodeLabel extends Record<string, string | object | undefined> {
    style: CSSProperties
    class: string[]
    html?: string | undefined
}

enum NodeType {
    CLUSTER = 'cluster',
    HIDDEN = '',
    USER = 'u',
    USER_TOXIC = 'ut',
    FACE = 'fs',
    REQUISITE = 'r',
    REQUISITE_OTHER = 'ro',
    REQUISITE_QIWI = 'rq',
    REQUISITE_CC = 'rcc',
    REQUISITE_WM = 'rwm',
} // See CidVisNodeFactory::TYPE_*

interface ImageDescription {
    path: string
    description: string
}

interface FormConfig extends FormGridType {
    cidForm: FormGridType
    preloadImages: string[]
    imageDescriptions: ImageDescription[]
    imagesTypes: Record<string, string>
}

interface ResponseStat {
    sites: SiteBrandRow[]
    sitesBrands: SiteBrandRow[]
    requisites: RequisiteRow[]
    sitesTotalRowName: string
}

interface GraphNodeData extends Record<string, unknown> {
    ico?: string // icon name
    c?: string // text to copy
    s?: number // icon size
    u?: undefined | string | string[]
    t: NodeType
    d?: string
    i?: string
    cid?: number // cloud id
    sid?: number // site id
    bid?: number // brand id
    faceId?: number
    asyncInfo?: boolean
    asyncInfoError?: boolean
    children?: GraphNode[]
    cluster?: boolean
    origin?: GraphNodeData
}

type GraphNode = Node<GraphNodeData>

interface CloudResponse {
    cid: number[]
    type: string
    nodes: [string, GraphNodeData][]
    links: [string, NodeId][]
    stat: ResponseStat,
    cluster: { expand: number[] }
}

interface GraphRuntime {
    graph: Graph<GraphNodeData>
    renderer: Renderer
    nodeProgram: WebglImageNodeProgram
}

const ICON_MULTI_CLUSTER_ALIAS = 'multi-source-cluster'
const CLUSTERING_LIMIT_DEFAULT = 2000

const g: GraphRuntime = {
    graph: undefined as unknown as Graph<GraphNodeData>,
    renderer: undefined as unknown as Renderer,
    nodeProgram: undefined as unknown as WebglImageNodeProgram,
}

export default defineComponent({
    components: {
        Icona,
        Popup,
        RequisitesTable,
        SitesTable,
        Highlight,
        Card,
        FormGrid,
    },
    beforeRouteEnter (to, _from, next: NextCurrent) {
        next(async vm => {
            await vm.loadConfig()
            await vm.init({})
            await vm.processQueryParams(to.query, to.hash)
        })
    },
    setup() {
        return {
            theme: useTheme(),
        }
    },
    data () {
        return {
            nodeTypes: NodeType,
            cidForm: {} as FormGridType,
            activeSites: [] as SiteBrandRow[],
            preloadImages: [] as string[],
            imageDescriptions: [] as ImageDescription[],
            imagesTypes: {} as Record<string, string>,

            faqPopupOpened: false,

            isPaused: false,
            isLabelsVisible: false,
            highlightedNodes: [] as NodeType[],
            searchTerm: null as string | null,
            searchRequisites: [] as string[],
            requisiteMaxYear: null as number | null,
            domLabels: {} as Record<string, NodeLabel>,

            selectedNode: null as GraphNode | null,
            selectedLinks: [] as LinkId[],

            inProgress: false,
            statInProgress: false,
            currentCid: false as number | false,
            currentType: false as string | false,

            visGraphStyle: {},

            sitesData: [] as SiteBrandRow[],
            sitesBrandsData: [] as SiteBrandRow[],
            sitesTotalRowName: '' as string,
            sitesTableDisplayWithBrand: true as boolean,

            requisitesData: [] as RequisiteRow[],
            blockReasons: {} as Record<string, string>,
            faceImages: [] as { url: string, visible: boolean }[],
            faceGallery: {} as LuminousGallery,
            faceDataLoadInProgress: false,
            faceImageLoadInProgress: false,

            ngrChartLoadInProgress: false,
            ngrChartPopupOpened: false,
            chartsData: {} as object,
            intervalLive: undefined,
            debugInfo: {} as {
                _: Date,
                usersCount?: number,
                clusteringLastWhisper?: Record<string, number | string>,
                clusteringLastLouvain?: Record<string, number | string>
            },
            clusterChildren: {} as Record<string, NodeId>,
            linksBeforeClustering: {} as Record<string, NodeId[]>,
            clusterToUsersLinks: {} as Record<string, string[]>,
            usersToClustersLinks: {} as Record<string, NodeId[]>,

            liveCallingPool: undefined as number | undefined,
            betweenness: [] as string[],
            currentCloud: undefined as number | undefined,
        }
    },

    computed: {
        searchTermLower () {
            return (this.searchTerm || '').toLowerCase()
        },
        currentSitesTableData () {
            return this.sitesTableDisplayWithBrand ? this.sitesBrandsData : this.sitesData
        },
        selectedNodeBottomInfo() {
            if (!this.selectedNode) {
                return {}
            }
            const fullInfo = this.selectedNode.data.d ?? this.selectedNode.data.i ?? ''
            if (this.selectedNode.data?.c) {
                const copyContent = this.selectedNode.data.c
                const startAndBegin = fullInfo.split(copyContent)

                return {
                    start: startAndBegin[0],
                    copyContent: copyContent,
                    end: startAndBegin[1] || '',
                }
            }

            return {
                start: fullInfo,
            }
        },
    },

    mounted () {
        this.$nextTick(() => {
            const rect = (this.$refs.graph as Element).getBoundingClientRect()
            const win = (this.$refs.graph as Element).ownerDocument.defaultView || { pageYOffset: 0 }
            this.visGraphStyle = {
                height: `${window.innerHeight - rect.top + win.pageYOffset - 20}px`,
                maxHeight: `${window.innerHeight - rect.top + win.pageYOffset - 20}px`,
            }
        })
    },

    methods: {
        async loadConfig () {
            const data = await this.$fetch<FormConfig>('/tools/cid-visualisation/config')
            this.cidForm = data.cidForm
            this.preloadImages = data.preloadImages
            this.imageDescriptions = data.imageDescriptions
            this.imagesTypes = data.imagesTypes
        },

        processQueryParams (query: LocationQuery, hash: string) {
            this.cidForm.values = query as Values

            switch (hash) {
                case '#cid':
                    return this.onCidSubmit(this.cidForm.values)
            }
        },

        onCidChange (values: Values) {
            this.cidForm.values = values
            // todo replace with router. vue 3.0.11 broken reactivity
            // this.$router.replace({query: this.cidForm.values});
            history.replaceState({}, '', this.$route.path + '?' + this.$encodeParams(this.cidForm.values))
        },

        onCidSubmit (values: Values) {
            this.inProgress = true

            this.$processFormResponse<CloudResponse>(this.$fetch('/tools/cid-visualisation/data', values), this.cidForm)
                .then(this.loadData)
                .finally(() => this.inProgress = false)
        },

        copy (text: string) {
            copyToClipboard(text)
        },

        init () {
            document.addEventListener('keyup', event => this.liveCallingPool = event.code === 'Escape' ? undefined : this.liveCallingPool)
            document.addEventListener('keyup', event => {
                if (event.altKey && event.code === 'KeyI') {
                    const info = JSON.stringify({
                        ...this.debugInfo,
                        nodesCountNow: this.countNodes(g.graph),
                        ...{ selectedNode: this.selectedNode },
                    }, null, 4)
                    // eslint-disable-next-line
                    console.log(info)
                }
            })

            g.graph = Viva.Graph.graph({ multigraph: true })

            const graphics = Viva.Graph.View.webglGraphics({
                clearColor: true,
                clearColorValue: this.theme.isDarkTheme.value ? '#333333FF' : '#FFFFFFFF',
                premultipliedAlpha: false,
            })

            g.nodeProgram = Viva.Graph.View.webglImageNodeProgram(4096)
            graphics.setNodeProgram(g.nodeProgram)

            graphics.node((node: GraphNode) => {
                const size = node.data.s
                return Viva.Graph.View.webglImage(size, window.location.origin + node.data.ico)
            })

            graphics.link(() => {
                return Viva.Graph.View.webglLine('#BBBBBB')
            })

            graphics.placeNode((ui: { node: GraphNode }, pos: NodePosition) => {
                // This callback is called by the renderer before it updates
                // node coordinate. We can use it to update corresponding DOM
                // label position;

                // we create a copy of layout position
                const domPos = {
                    x: pos.x,
                    y: pos.y,
                }
                // And ask graphics to transform it to DOM coordinates:
                graphics.transformGraphToClientCoordinates(domPos)

                // then move corresponding dom label to its own position:
                const nodeId = ui.node.id

                if (this.domLabels[nodeId]) {
                    const label = this.domLabels[nodeId]

                    label.style = { ...label.style, left: domPos.x + 'px', top: domPos.y + 'px' }
                    const graph = this.$refs.graph as { clientHeight: number, clientWidth: number }

                    if (domPos.y < 0 || domPos.y > graph.clientHeight || domPos.x < 0 || domPos.x > graph.clientWidth) {
                        if (!label.class.includes('d-none')) {
                            label.class.push('d-none')
                        }
                    } else if (domPos.y > 0 || domPos.y > graph.clientHeight || domPos.x > 0 || domPos.x > graph.clientWidth) {
                        label.class = label.class.filter((cl) => cl !== 'd-none')
                    }
                }

                return pos
            })

            const layout = Viva.Graph.Layout.forceDirected(g.graph, {
                springLength: 40,
                springCoeff: 0.0004,
                dragCoeff: 0.01,
                gravity: -7,
                theta: 0.9,
            })

            const events = Viva.Graph.webglInputEvents(graphics)

            g.renderer = Viva.Graph.View.renderer(g.graph, {
                container: this.$refs.graph,
                layout,
                graphics,
                renderLinks: true,
                prerender: true,
            })

            events.click(async (node: GraphNode, click: MouseEvent) => {
                if (!click.ctrlKey) {
                    this.onNodeSelect(node)
                    return
                }

                this.onNodeSelect(undefined)
                const isSourceClusterClicked = this.isSourceClusterData(node.data)
                const isMultiClusterClicked = this.isMultiSourceClusterData(node.data)

                if (isSourceClusterClicked || (isMultiClusterClicked && click.shiftKey)) {
                    if (!click.altKey) {
                        await this.loadClustersUsers(node)
                        if (isSourceClusterClicked) {
                            this.onNodeSelect(g.graph.getNode(node.id))
                        }
                    } else {
                        await this.chainCall(node, this.loadClustersUsers, (n: GraphNode) => {
                            return this.isSourceClusterData(n.data) || (this.isMultiSourceClusterData(n.data) && click.shiftKey)
                        })
                    }
                } else if (isMultiClusterClicked) {
                    if (!click.altKey) {
                        await this.expandMultiClusterNode(node)
                    } else {
                        await this.chainCall(node, this.expandMultiClusterNode, (n: GraphNode) => {
                            return this.isMultiSourceClusterData(n.data)
                        })
                    }
                }
            })

            events.dblClick((node: GraphNode) => {
                this.onNodeSelect(undefined)
                g.graph.removeNode(node.id)
            })

            g.renderer.run()

            return this.loadAtlas()
        },

        loadAtlas () {
            if (this.preloadImages.length === 0) {
                // eslint-disable-next-line
                console.error("preloadImages aren't defined")
                return
            }

            const atlas = g.nodeProgram.atlas()
            return Promise.all(this.preloadImages.map(img => {
                return new Promise((resolve, reject) => {
                    atlas.load(img, resolve)
                    setTimeout(reject, 3000)
                })
            }))
        },

        togglePause () {
            this.isPaused = !this.isPaused
            if (this.isPaused) {
                this.highlight()
                g.renderer.pause()
            } else {
                this.isLabelsVisible = false
                this.domLabels = {}
                g.renderer.resume()
            }
        },

        onNodeSelect (node: GraphNode | undefined) {
            const graphics = g.renderer.getGraphics()
            this.selectedNode = node ? this.selectedNode = Object.assign({}, node) : null // copy without reactivity

            this.selectedLinks.forEach((linkId) => {
                const linkUI = graphics.getLinkUI(linkId)
                if (linkUI) {
                    linkUI.color = 0xBBBBBBFF
                }
            })
            this.selectedLinks = []

            if (node) {
                // CID extraction
                if (node.data.cid) {
                    this.currentCid = node.data.cid
                }

                g.graph.forEachLinkedNode(node.id, (_linkedNode: GraphNode, link: Link) => {
                    this.selectedLinks.push(link.id)
                    const linkUI = graphics.getLinkUI(link.id)
                    linkUI.color = 0xFF0000FF
                }, false)
            }

            g.renderer.rerender()

            this.faceImages = []
            this.faceImageLoadInProgress = false

            const faceList = this.$refs['face-list'] as Element | undefined
            if (faceList) {
                faceList.scrollLeft = 0
            }
            if (!node) {
                return
            }

            if (node.data.asyncInfo) {
                node.data.asyncInfoError = false
                this.$fetch('/tools/cid-visualisation/source-info', { key: node.id }).then((data: { nodes: [string, GraphNodeData][] }) => {
                    data.nodes.forEach(([id, nodeData]) => {
                        const loadedNode = g.graph.getNode(id)
                        if (loadedNode === undefined) {
                            return
                        }

                        if (nodeData.u && nodeData.u.length && this.isSourceClusterData(loadedNode.data)) {
                            this.sourceClusterDataPrepare(nodeData)
                        } else {
                            this.addIcon(nodeData)
                        }

                        Object.assign(loadedNode.data, nodeData, { asyncInfo: false })  // copy without reactivity
                        this.selectedNode = Object.assign({}, loadedNode)
                    })
                }).catch((e: Error) => {
                    node.data.asyncInfoError = true
                    throw e
                })
            }

            if (node.data.t === NodeType.FACE && !this.isSourceClusterData(node.data)) {
                this.faceDataLoadInProgress = true
                this.$fetch('/tools/cid-visualisation/faces-documents', { faceId: node.data.faceId }).then((data: GraphNodeData) => {
                    this.faceImages = Object.entries(data).map(([, v]) => {
                        return { url: v as string, visible: false }
                    })
                    this.initGallery()
                }).finally(() => {
                    this.faceDataLoadInProgress = false
                    this.$nextTick(() => this.loadNextVisibleFace())
                })
            }
        },

        onToggleLabelsVisibility () {
            this.isLabelsVisible = !this.isLabelsVisible

            if (this.isLabelsVisible) {
                this.highlight()
            }
        },

        onShowCentrality () {
            const result = Viva.Graph.centrality().betweennessCentrality(g.graph)
            this.betweenness = result.map((v: { key: string }) => v.key)
        },

        onNextNode () {
            let currentIndex = this.selectedNode !== null ? this.betweenness.findIndex(n => n === this.selectedNode?.id) : -1

            while (++currentIndex < this.betweenness.length) {
                const newNode = g.graph.getNode(this.betweenness[currentIndex])

                if (newNode && newNode.links.length < 6) {
                    this.onNodeSelect(newNode)
                    this.moveToNode(newNode)
                    return
                }
            }

            alert('List of node ended')
            this.onNodeSelect(undefined)
        },

        onHighlightSearch (searchTerm: string) {
            this.searchTerm = searchTerm
            this.isLabelsVisible = true
            this.searchRequisites = []
            this.highlight()
        },

        onHighlightSites (site: SiteBrandRow) {
            const siteIdx = this.activeSites.findIndex(
                (activeSite: SiteBrandRow) => site.site_id && activeSite.site_id === site.site_id && (site.brand_id === null || activeSite.brand_id === site.brand_id),
            );

            if (siteIdx === -1) {
                this.activeSites.push(site)
            } else {
                this.activeSites.splice(siteIdx, 1)
            }

            this.isLabelsVisible = true
            this.highlight()
        },

        onToggleNodeHighlight(value: NodeType) {
            const index = this.highlightedNodes.indexOf(value);
            if (index === -1) {
                this.highlightedNodes.push(value)
            } else {
                this.highlightedNodes.splice(index, 1)
            }
            this.isLabelsVisible = true
            this.highlight()
        },

        moveToNode (node: GraphNode) {
            const nodePosition = g.renderer.getLayout().getNodePosition(node.id)
            g.renderer.moveTo(nodePosition.x, nodePosition.y)
        },

        highlight () {
            this.domLabels = this.generateLabels()
            g.renderer.rerender()

            if (this.isLabelsVisible && !this.isPaused) {
                this.togglePause()
            }
        },
        toggleSitesTable() {
            this.sitesTableDisplayWithBrand = !this.sitesTableDisplayWithBrand
        },
        loadData (data: CloudResponse) {
            this.onNodeSelect(undefined)
            g.graph.clear()
            this.isLabelsVisible = false
            this.debugInfo = { _: new Date() }
            this.domLabels = {}
            this.activeSites = []
            this.currentCid = data.cid[0]
            this.currentType = data.type
            this.currentCloud = Math.random()
            this.liveCallingPool = undefined
            this.clusterChildren = {}
            this.clusterToUsersLinks = {}
            this.usersToClustersLinks = {}

            if (this.cidForm.values?.mode === 'clusters') {
                let clusterGraph = createGraph()

                data.nodes.forEach(([nodeId, nodeData]: [string, GraphNodeData]) => {
                    if (this.isRequisiteType(nodeData.t)) {
                        nodeData.asyncInfo = true
                    }
                    if (nodeData.u && nodeData.u.length) {
                        this.sourceClusterDataPrepare(nodeData);
                        (nodeData.u as string[]).forEach((uKey: string) => {
                            this.usersToClustersLinks[uKey] = this.usersToClustersLinks[uKey] || []
                            this.usersToClustersLinks[uKey].forEach(linkTo => data.links.push([nodeId, linkTo]))
                            this.usersToClustersLinks[uKey].push(nodeId)
                        })
                    } else {
                        this.addIcon(nodeData)
                    }
                    clusterGraph.addNode(nodeId, nodeData)
                })

                this.debugInfo.usersCount = Object.keys(this.usersToClustersLinks).length

                // two directions links. important for expand clusters
                data.links.forEach(([linkFrom, linkTo]) => [[linkFrom, linkTo], [linkTo, linkFrom]].forEach(([linkFrom, linkTo]) => {
                    clusterGraph.addLink(linkFrom, linkTo)
                    this.linksBeforeClustering[linkFrom] = this.linksBeforeClustering[linkFrom] || []
                    this.linksBeforeClustering[linkFrom].push(linkTo)
                }))

                const currentLimit = (this.$route.query.limit as unknown as number | undefined) || CLUSTERING_LIMIT_DEFAULT
                if (this.countNodes(clusterGraph) > currentLimit) {
                    clusterGraph = this.clusteredLouvain(clusterGraph)
                }

                clusterGraph.forEachNode(node => {
                    if (Array.isArray(node.data)) {
                        node = this.multiSourceClusterNode(node as GraphNode)
                    }
                    g.graph.addNode(node.id, node.data)
                })
                clusterGraph.forEachLink(link => {
                    g.graph.addLink(link.fromId, link.toId)
                })
                const clustersToExpand = Array.from(new Set(Object.values(data.cluster.expand || []).map(id => clusterGraph.getNode(this.clusterChildren[id] || id)).filter(n => n))) as GraphNode[]
                this.intervalCall(clustersToExpand, (node: GraphNode) => clusterGraph.getNode(node.id) ? this.loadClustersUsers(node) : undefined)
            } else {
                data.nodes.forEach(([key, data]) => {
                    this.addIcon(data)
                    g.graph.addNode(key, data)
                })

                data.links.forEach((d) => {
                    g.graph.addLink(d[0], d[1])
                })
            }

            this.setStat(data.stat)

            if (this.isPaused) {
                this.isPaused = false
                g.renderer.resume()
            }
        },
        setStat (stat: ResponseStat) {
            stat = stat || {}
            this.sitesData = stat.sites || []
            this.sitesBrandsData = stat.sitesBrands || []
            this.requisitesData = stat.requisites || []
            this.sitesTotalRowName = stat.sitesTotalRowName
        },
        async onHighlightOlds () {
            this.requisiteMaxYear = parseInt(
                prompt('Enter year', (this.requisiteMaxYear || (new Date().getFullYear() - 3)) + '') as string,
            ) || this.requisiteMaxYear
            if (!this.requisiteMaxYear) {
                return
            }

            const date = this.requisiteMaxYear + '-12-31'

            if (this.requisitesData.length === 0) {
                this.statInProgress = true
                await this.$fetch('/tools/cid-visualisation/stat', this.cidForm.values)
                    .then((r: { stat: ResponseStat }) => this.setStat(r.stat))
                    .finally(() => this.statInProgress = false)
            }

            this.isPaused = true
            this.isLabelsVisible = true
            g.renderer.pause()
            this.searchRequisites = this.requisitesData.filter(v => v.max_date !== undefined && v.max_date < date).map(v => v.requisite)
            this.highlight()
        },
        generateLabels () {
            const labels = Object.create(null)
            if (!this.isLabelsVisible) {
                return labels
            }
            const eachNode = g.graph.forEachNode as (handler: (node: GraphNode) => void) => void
            eachNode((node: GraphNode) => {
                const label = this.generateNodeLabel(node)
                if (label) {
                    labels[node.id] = label
                }
            })
            return labels
        },
        updateNodeLabel (node: GraphNode | undefined) {
            if (node === undefined) {
                return
            }

            if (!this.isLabelsVisible) {
                return
            }
            this.domLabels[node.id] = this.generateNodeLabel(node) as NodeLabel
            if (!this.domLabels[node.id]) {
                delete this.domLabels[node.id]
            }
        },
        generateNodeLabel (node: GraphNode): undefined | NodeLabel {
            const selected = this.isNodeLabelHighlighted(node)

            if (!node.data.l && !selected) {
                return
            }
            return {
                class: ['node-label', ...(selected ? ['selected'] : [])],
                html: node.data.l || node.data.d || node.data.i,
            } as NodeLabel
        },

        isNodeLabelHighlighted(node: GraphNode): boolean {
            if (this.highlightedNodes.includes(node.data.t)) {
                return true
            }

            if (this.searchRequisites.length && ((node.data.children && node.data.children.some(c => this.searchRequisites.includes(c.data.c + ''))) || this.searchRequisites.includes(node.data.c + ''))) {
                return true
            }

            if (this.isUserNode(node) && this.activeSites.some((site: SiteBrandRow) => node.data.sid && site.site_id === node.data.sid && site.brand_id === node.data.bid)) {
                return true
            }

            const searchSource = ((Array.isArray(node.data.i) ? node.data.i.join(',') : node.data.i) + '\n' + node.data.l).toLowerCase()
            if (this.searchTermLower && searchSource.includes(this.searchTermLower)) {
                return true
            }

            return false
        },
        removeLabel (nodeId: NodeId) {
            delete this.domLabels[nodeId]
        },
        openFaqPopup (open = true) {
            this.faqPopupOpened = open
        },
        openChartPopup (open = true) {
            if (open) {
                this.loadNgrChartPopup()
            }

            this.ngrChartPopupOpened = open
        },
        loadNgrChartPopup () {
            this.ngrChartLoadInProgress = true

            this.$fetch('/tools/cid-visualisation/ngr-chart', {
                cid: this.currentCid,
                type: this.currentType,
            })
                .then((data: object) => {
                    this.chartsData = data
                    this.$nextTick(() => {
                        HighchartsLoader().then(({ default: Highcharts }) => {
                            Highcharts.chart('ngr-chart', this.chartsData)
                        })
                    })
                })
                .finally(() => {
                    this.ngrChartLoadInProgress = false
                })
        },
        initGallery () {
            if ('luminousInstances' in this.faceGallery) {
                this.faceGallery.luminousInstances.forEach(f => f.destroy())
                this.faceGallery = {} as LuminousGallery
            }

            if (this.faceImages.length > 0) {
                this.$nextTick(() => this.faceGallery = new LuminousGallery(document.querySelectorAll('.face-image')))
            }
        },
        isElementInViewport (el: Element) {
            const rect = el.getBoundingClientRect()

            return (// vertInView
                (rect.top <= (window.innerHeight || document.documentElement.clientHeight)) && ((rect.top + rect.height) >= 0) && // horInView
                (rect.left <= (window.innerWidth || document.documentElement.clientWidth)) && ((rect.left + rect.width) >= 0))
        },
        loadNextVisibleFace () {
            if (!this.$refs['face-list'] || this.faceImageLoadInProgress) {
                return
            }

            for (const img of this.faceImages) {
                if (img.visible) {
                    continue
                }
                const elements = this.$refs[`img-${img.url}`] as Element[]
                for (const i in elements) {
                    if (this.isElementInViewport(elements[i])) {
                        this.faceImageLoadInProgress = true
                        img.visible = true
                        return
                    }
                }
            }
        },
        afterFaceImageLoading (event: Event, success: boolean) {
            this.faceImageLoadInProgress = false
            this.loadNextVisibleFace()

            if (event.target && event.target instanceof Element) {
                if (success) {
                    this.initGallery()
                    event.target.classList.remove('img-not-loaded', 'progress-bar-animated')
                } else {
                    event.target.classList.remove('progress-bar-animated', 'bg-secondary')
                    event.target.classList.add('bg-danger')
                }
            }
        },
        onResetValues () {
            this.cidForm.values = {}
            this.$router.replace({ query: {} })
            this.sitesData = []
            this.requisitesData = []
            this.chartsData = []
            this.faceImages = []
            this.faceGallery = {} as LuminousGallery
            g.graph.clear()
            this.currentCid = false
            this.currentType = false
        },
        countNodes (graph: Graph) {
            let count = 0
            graph.forEachNode(() => {
                count++
            })
            return count
        },
        clusteredLouvain (graph: Graph) {
            const clusteringStartAt = new Date()
            const countBefore = this.countNodes(graph)
            const clusteredGraph = coarsen(graph, detectCommunities(graph))

            const countAfter = this.countNodes(clusteredGraph)

            this.debugInfo = {
                ...this.debugInfo,
                clusteringLastLouvain: {
                    countBefore,
                    countAfter,
                    duration: (new Date().getTime() - clusteringStartAt.getTime()) / 1000 + 's',
                },
            }

            clusteredGraph.forEachNode((node: Node) => {
                node.data = Array.from(node.data as number[]).map(nodeId => graph.getNode(nodeId))
            })
            return clusteredGraph
        },
        isUserNode (node: GraphNode): boolean {
            return node.data.t === NodeType.USER || node.data.t === NodeType.USER_TOXIC
        },
        isMultiSourceClusterData (data: GraphNodeData): boolean {
            return data.children !== undefined
        },
        isSourceClusterData (data: GraphNodeData) {
            return data.cluster === true
        },
        isRequisiteType (type: string) {
            return ([NodeType.REQUISITE, NodeType.REQUISITE_CC, NodeType.REQUISITE_QIWI, NodeType.REQUISITE_WM, NodeType.REQUISITE_OTHER] as string[]).includes(type)
        },
        sourceClusterDataPrepare (data: GraphNodeData) {
            data.u = data.u !== undefined ? (data.u as string).split(',') : []
            data.cluster = true
            this.addIcon(data)
            data = this.sourceClusterInfoPrepare(data)
            data.s = (data.u as string[]).length === 0 ? data.s : (((data.s || 0) ** 4) * (data.u as string[]).length) ** (1 / 4)
            return data
        },
        sourceClusterInfoPrepare (data: GraphNodeData) {
            data.origin = {
                i: data.i,
                l: data.l,
                d: undefined,
                t: data.t,
            }
            data.d = data.i + ((data.u as string[]).length > 0 ? '. Site-users (' + (data.u as string[]).length + ')' : '')
            data.i = data.d + '\n' + data.u + '\n' + data.l
            data.l = data.d
            return data
        },
        multiSourceClusterNode (node: GraphNode) {
            const children = this.nodeChildrenTreeToList(node.data as unknown as GraphNode[]) as GraphNode[]
            const users = Array.from(new Set([].concat(...children.map(n => n.data.u) as never[][])))
            children.forEach(n => this.clusterChildren[n.id] = node.id)
            node.data = {
                d: 'Cloud Sources(' + children.length + ')' + (users ? ', Site Users(' + users.length + ')' : ''),
                i: children.map(n => n.data.i).join(', '),
                s: children.map(node => (node.data.s || 0) ** 4).reduce((a, b) => a + b, 0) ** (1 / 4),
                t: NodeType.CLUSTER,
                ico: node.data.ico,
                children,
                u: users,
            } as GraphNodeData

            this.addIcon(node.data)
            return node
        },
        reAddNodeNearPos (id: NodeId, data: GraphNodeData, position: NodePosition | undefined) {
            const links = [] as NodeId[]
            if (g.graph.getNode(id)) {
                g.graph.forEachLinkedNode(id, (linkedNode: GraphNode) => {
                    links.push(linkedNode.id)
                }, false)
                g.graph.removeNode(id)
            }
            g.graph.addNode(id, data)
            links.forEach(link => g.graph.addLink(id, link))

            if (position === undefined) {
                return
            }
            const p = g.renderer.getLayout().getNodePosition(id)
            p.x = position.x + (Math.random() * 18 + 1) * (Math.random() >= 0.5 ? -1 : 1)
            p.y = position.y + (Math.random() * 18 + 1) * (Math.random() >= 0.5 ? -1 : 1)
            this.updateNodeLabel(g.graph.getNode(id))
        },
        expandMultiClusterNode (node: GraphNode) {
            (node.data.children || []).forEach(n => {
                this.reAddNodeNearPos(n.id, n.data, g.renderer.getLayout().getNodePosition(node.id))
                delete this.clusterChildren[n.id];
                (this.clusterToUsersLinks[n.id] || []).forEach(userLink => {
                    if (g.graph.getNode(userLink)) {
                        g.graph.addLink(n.id, userLink)
                    }
                })
                delete this.clusterToUsersLinks[n.id]
            });

            (node.data.children || []).forEach(n => {
                (this.linksBeforeClustering[n.id] || []).forEach(link => {
                    while (this.clusterChildren[link] !== undefined) {
                        link = this.clusterChildren[link]
                    }
                    if (g.graph.getNode(link)) {
                        g.graph.addLink(n.id, link)
                    } else {
                        // eslint-disable-next-line
                        console.warn('Linked node not found', link, 'for', n.id)
                    }
                })
            })

            this.removeLabel(node.id)
            g.graph.removeNode(node.id)
            if (this.isPaused) {
                g.renderer.rerender()
            }

            return Promise.resolve((node.data.children || []).length * 5) // timeout in ms if needed
        },
        clusterToSimpleData (clusterData: GraphNodeData) {
            const data = clusterData
            const origin = data.origin as GraphNodeData
            if (data.origin) {
                Object.keys(data.origin).forEach(k => {
                    if (origin[k] === undefined) {
                        delete data[k]
                    } else {
                        data[k] = origin[k]
                    }
                })
            }
            delete data.origin
            delete data.u
            delete data.cluster
            this.addIcon(data)
            return data
        },
        clusterToSimpleNode (node: GraphNode | undefined) {
            if (node === undefined) {
                return
            }

            const data = this.clusterToSimpleData(node.data)
            let deleted: number
            do { // wtf with link ???
                deleted = 0
                g.graph.forEachLinkedNode(node.id, (linkedNode: GraphNode, link: Link) => {
                    if (this.isSourceClusterData(linkedNode.data) || this.isMultiSourceClusterData(linkedNode.data)) {
                        g.graph.removeLink(link)
                        deleted++
                    }
                }, false)
            } while (deleted > 0)

            this.reAddNodeNearPos(node.id, data, g.renderer.getLayout().getNodePosition(node.id))
        },
        loadClustersUsers (node: GraphNode): Promise<number | undefined> {
            const currentCloud = this.currentCloud
            if (this.isMultiSourceClusterData(node.data)) {
                (node.data.children || []).forEach(n => delete this.clusterChildren[n.id])
            }

            const simpleClustersNearBy = []
            g.graph.forEachLinkedNode(node.id, (linkedNode: GraphNode) => {
                if (this.isSourceClusterData(linkedNode.data) && (linkedNode.data.u || []).length === 1) {
                    simpleClustersNearBy.push(linkedNode.id)
                }
            }, false)

            const allSiteUsers = this.isMultiSourceClusterData(node.data) ? Array.from(new Set([].concat(...(node.data.children || []).map(n => n.data.u || []) as never[]))) : (node.data.u || []) as string[]
            const hiddenSiteUsersList = allSiteUsers.filter(u => u.substring(0, 2) === 'u-')
            const visibleUsers = allSiteUsers.filter(u => !hiddenSiteUsersList.includes(u))

            const linkedClusters = [] as GraphNode[]
            const nodesToSimplify = this.isSourceClusterData(node.data) ? [node.id] : (node.data.children || []).filter(n => this.isSourceClusterData(n.data)).map(n => n.id)
            nodesToSimplify.map(nId => g.graph.getNode(nId)).filter(n => n).forEach(n => {
                if (n === undefined) {
                    return
                }

                g.graph.forEachLinkedNode(n.id, (linkedNode: GraphNode) => {
                    if (this.isSourceClusterData(linkedNode.data)) {
                        linkedClusters.push(linkedNode)
                    }
                }, false)
            })

            let position: NodePosition

            if (visibleUsers.length === 0) {
                return new Promise(resolve => {
                    position = g.renderer.getLayout().getNodePosition(node.id)
                    if (this.isMultiSourceClusterData(node.data)) {
                        this.expandMultiClusterNode(node)
                    }
                    resolve(undefined)
                })
            } else {
                return this.$fetch('/tools/cid-visualisation/users', {
                    users: visibleUsers.join('\n'),
                }).then(data => {
                    if (currentCloud !== this.currentCloud) {
                        return undefined
                    }
                    position = g.renderer.getLayout().getNodePosition(node.id)

                    if (this.isMultiSourceClusterData(node.data)) {
                        this.expandMultiClusterNode(node)
                    }

                    const nodes = Object.values(data.nodes) as [string, GraphNodeData][]
                    nodes.forEach(([id, data]) => {
                        this.addIcon(data)
                        this.reAddNodeNearPos(id, data, position)
                    });
                    (Object.values(data.links) as [string, NodeId][]).forEach(([fromId, toId]) => {
                        if ((fromId + '').substring(0, 2) === 'u-') {
                            this.clusterToUsersLinks[toId] = this.clusterToUsersLinks[toId] || []
                            this.clusterToUsersLinks[toId].push(fromId)
                        }
                        while (this.clusterChildren[toId]) {
                            toId = this.clusterChildren[toId]
                            delete this.clusterChildren[toId]
                        }
                        if (g.graph.getNode(toId)) {
                            g.graph.addLink(fromId, toId)
                        }
                    })

                    // logic block

                    if (this.isMultiSourceClusterData(node.data)) {
                        g.graph.removeNode(node.id)
                    }
                    hiddenSiteUsersList.forEach(uKey => {
                        this.reAddNodeNearPos(uKey, {
                            i: 'N/A',
                            t: NodeType.HIDDEN,
                            s: 13,
                            ico: '/images/cid/question-sign.png',
                        }, position)
                        this.usersToClustersLinks[uKey].forEach(linkToSource => {
                            while (this.clusterChildren[linkToSource] !== undefined) {
                                linkToSource = this.clusterChildren[linkToSource]
                            }
                            g.graph.addLink(uKey, linkToSource)
                        })
                    })

                    // logic block

                    nodesToSimplify.map(nId => g.graph.getNode(nId)).filter(n => n).forEach(n => this.clusterToSimpleNode(n))
                    linkedClusters.map(n => g.graph.getNode(n.id)).filter(n => n && this.isSourceClusterData(n.data)).forEach(cluster => {
                        if (cluster === undefined) {
                            return
                        }
                        const usersCount = cluster.data.u ? cluster.data.u.length : 0
                        const loadedUsers = [] as NodeId[]

                        g.graph.forEachLinkedNode(cluster.id, (linkedNode: GraphNode) => {
                            if (this.isUserNode(linkedNode)) {
                                loadedUsers.push(linkedNode.id)
                            }
                        }, false)
                        if (usersCount === Array.from(new Set(loadedUsers)).length) {
                            this.clusterToSimpleNode(cluster)
                        }
                    })
                    return nodes.length * 5 // timeout in ms if needed;
                }).catch((response: Response) => {
                    try {
                        response.json().then(e => {
                            const errors = Object.values(e.form) as string[]
                            if (errors.length === 0) {
                                throw new Error()
                            }
                            [].concat(...errors as never[]).forEach(e => this.$notify({
                                type: 'error',
                                message: e,
                            }))
                        })
                    } catch (e) {
                        // eslint-disable-next-line
                        console.log(response)
                        // eslint-disable-next-line
                        console.error(e)
                    }
                    return new Promise(resolve => setTimeout(resolve, 1000))
                })
            }
        },
        async chainCall (startNode: GraphNode, action: (node: GraphNode) => Promise<number | undefined>, checker: (node: GraphNode) => boolean) {
            const toExpand = [startNode.id]

            const liveCallingPool = Math.random()
            this.liveCallingPool = liveCallingPool
            while (toExpand.length > 0 && liveCallingPool === this.liveCallingPool) {
                const nId = toExpand.shift()
                if (nId === undefined) {
                    continue
                }
                const node = g.graph.getNode(nId)
                if (!node || !checker(node)) {
                    continue
                }
                g.graph.forEachLinkedNode(node.id, (linkedNode: GraphNode) => {
                    if (checker(node)) {
                        toExpand.push(linkedNode.id)
                    }
                }, false)
                const timeout = await action(node)
                await new Promise(resolve => setTimeout(resolve, timeout || 0))
            }
        },
        async intervalCall (list: GraphNode[], action: (node: GraphNode) => Promise<number | undefined> | undefined) {
            const liveCallingPool = Math.random()
            this.liveCallingPool = liveCallingPool
            while (list.length > 0 && liveCallingPool === this.liveCallingPool) {
                const timeout = await action(list.shift() as GraphNode)
                await new Promise(resolve => setTimeout(resolve, timeout || 0))
            }
        },
        nodeChildrenTreeToList (data: GraphNode[]) {
            const res = [] as GraphNode[]
            data.forEach(node => {
                if (Array.isArray(node.data)) {
                    this.nodeChildrenTreeToList(node.data).forEach(node => res.push(node))
                } else {
                    res.push(node)
                }
            })
            return res
        },
        addIcon (data: GraphNodeData) {
            if (this.isMultiSourceClusterData(data)) {
                data.ico = this.imagesTypes[ICON_MULTI_CLUSTER_ALIAS]
            } else {
                data.ico = this.imagesTypes[(this.isSourceClusterData(data) ? 'cluster-' : '') + data.t] || this.imagesTypes[NodeType.HIDDEN]
            }
            return data
        },
    },
})
</script>

<style lang="scss">
.cid-visualisation-container {
    > div {
        display: flex;
        flex-direction: column;
    }
}

.node-label {
    position: absolute;
    pointer-events: none;
    color: white;
    z-index: 10;
    background: rgba(0, 0, 0, 0.6);
    padding: 1px 2px;
    font-size: 11px;

    &.selected {
        background: rgba(255, 0, 0, 0.6);
        z-index: 15;
    }
}

.hor-img-list {
    overflow-y: hidden;
    overflow-x: auto;
    bottom: 0;
    height: 100px;
    margin-top: -100px;
    white-space: nowrap;
}

.img-not-loaded {
    width: 100px;
}
</style>
