<template>
    <div>
        <div class="input-group input-group-sm">
            <input
                v-model="searchTerm"
                type="text"
                class="form-control"
                placeholder="Highlight requisite"
                @keyup.enter="highlightSearch"
            >

            <button
                type="button"
                class="btn btn-primary"
                @click="highlightSearch"
            >
                <Icona name="icn-search" />
            </button>
            <button
                type="button"
                class="btn btn-secondary"
                @click="toggleSitesTable"
            >
                {{ sitesTableDisplayWithBrand ? 'Only sites' : 'Sites with brands' }}
            </button>
        </div>
        <template v-if="sites.length !== 0">
            <div class="btn-group btn-group-xs flex-wrap d-block mt-1">
                <button
                    v-for="site in sites"
                    type="button"
                    class="btn btn-secondary"
                    :class="{active: activeSites.some((activeSite: SiteBrandRow) => activeSite.site_id === site.site_id && activeSite.brand_id === site.brand_id)}"
                    @click="toggleActiveSite(site)"
                >
                    {{ site.row_name }}
                </button>
            </div>
        </template>
    </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue'
import Icona from '@/components/icona.vue'
import { SiteBrandRow } from '@/apps/tools/cid-visualisation/sites-table.vue'

export default defineComponent({
    components: { Icona },
    props: {
        sites: {
            type: Array as PropType<SiteBrandRow[]>,
            required: true,
        },
        activeSites: {
            type: Array as PropType<SiteBrandRow[]>,
            required: true,
        },
    },

    emits: ['onSearchSubmit', 'onToggleSite', 'onToggleSitesTable'],

    data () {
        return {
            searchTerm: null,
            sitesTableDisplayWithBrand: true as boolean,
        }
    },
    methods: {
        toggleActiveSite (site: SiteBrandRow) {
            this.$emit('onToggleSite', site)
        },
        highlightSearch () {
            this.$emit('onSearchSubmit', this.searchTerm)
        },
        toggleSitesTable() {
            this.sitesTableDisplayWithBrand = !this.sitesTableDisplayWithBrand;
            this.$emit('onToggleSitesTable')
        },
    },
})
</script>
