<template>
    <div
        ref="$el"
        class="form-grid"
        @keydown.enter="onEnter"
    >
        <component
            :is="size === 'xs' ? 'h5' : 'h4'"
            v-if="title"
            class="border-bottom pb-1"
        >
            {{ title }}
            <slot
                name="afterTitle"
            />
        </component>

        <div
            v-for="row in blocks"
            class="row"
        >
            <div
                v-for="cell in row"
                :key="`cell-${cell.name}`"
                :class="`col-md-${cell.width}`"
            >
                <div
                    class="form-group position-relative"
                    :class="{required: cell.required}"
                    :title="cell.tooltip || ''"
                >
                    <slot
                        name="cellTitle"
                        :cell="cell"
                    >
                        <div class="label-container">
                            <label
                                v-if="!cell.hideLabel"
                                class="me-1"
                                :for="makeCellId(cell)"
                            >{{ cell.title || '&nbsp;' }}</label>
                            <FormOperator
                                v-if="cell.operators"
                                :operators="cell.operators"
                                :value="displayValues[operatorName(cell)] as string"
                                @input="onChange({name: operatorName(cell), value: $event})"
                            />
                            <FormOperator
                                v-if="cell.units"
                                :operators="cell.units"
                                :value="displayValues[(cell.name + cell.unitPostfix)] as string"
                                class="ms-1"
                                @input="onChange({name: cell.name + cell.unitPostfix, value: $event})"
                            />
                            <slot
                                name="afterCellTitle"
                                :cell="cell"
                            />
                        </div>
                    </slot>

                    <slot
                        v-if="cell.slotName"
                        :name="cell.slotName"
                        v-bind="cell"
                    />
                    <component
                        :is="`el-${cell.type}`"
                        v-else
                        v-bind="cellProps(cell)"
                        @change="onChange({name: cell.name, value: $event})"
                        @submit="onSubmit"
                        @reset="onReset"
                    />

                    <FormError v-if="errors[cell.name]" :error="errors[cell.name]" />

                    <small
                        v-if="cell.hint"
                        class="form-text"
                    >
                        {{ cell.hint }}
                    </small>
                </div>
            </div>
        </div>
        <p
            v-for="(message, e) in errorsWithoutInput"
            :key="e"
            class="alert alert-danger m-3"
        >
            {{ e }} {{ message }}
        </p>
    </div>
</template>

<script lang="ts">
import {
    ElCheckbox,
    ElDate,
    ElFile,
    ElGroupSelect,
    ElList,
    ElMdEditor,
    ElMultipleSubmit,
    ElRadioList,
    ElButton,
    ElSelect,
    ElSelectSortable,
    ElTextArea,
    ElTextInput,
    ElTextInputLiveSearch,
    ElTextStatic,
} from './types'

export default {
    components: {
        ElCheckbox,
        ElDate,
        ElFile,
        ElGroupSelect,
        ElList,
        ElMdEditor,
        ElMultipleSubmit,
        ElRadioList,
        ElButton,
        ElSelect,
        ElSelectSortable,
        ElTextArea,
        ElTextInput,
        ElTextInputLiveSearch,
        ElTextStatic,
    },
}
</script>

<script lang="ts" setup generic="V extends Record<string, unknown> = ValuesOrFileList">
import FormError from './error.vue'
import FormOperator from './operator.vue'
import { _ } from '@/utils'
import { FormElement, FormGridType, ValuesOrFileList } from '@/types.ts'
import { computed, nextTick, ref, watch } from 'vue'

type ValuesNotNullable = NonNullable<typeof $props.values>

interface FormElementForComponent extends FormElement {
    id: string
    value: unknown
    isInvalid: boolean
}

const $props = withDefaults(defineProps<FormGridType<V>>(), {
    uniqueId: '',
    enabled: true,
    blocks: () => [],
    size: 'md',
    errors: () => ({}),
    enctype: 'json',
})

const $emit = defineEmits<{
    submit: [values: V]
    submitFormData: [formData: FormData]
    change: [values: V, changed: V]
    reset: []
}>()

const $el = ref<HTMLElement | null>(null)

const showValidation = ref(true)

let keepFocusEl: HTMLElement | null = null
let displayValues: ValuesNotNullable = $props.values || {} as V

const errorsWithoutInput = computed(() => {
    const result = { ...$props.errors }
    $props.blocks.forEach(r => {
        r.forEach(c => {
            if (c.name in result) {
                delete result[c.name]
            }
        })
    })

    return result
})

watch(() => $props.enabled, enabled => {
    if (!enabled) {
        keepFocusEl = $el.value?.contains(document.activeElement)
            ? document.activeElement as HTMLElement
            : null
    } else if (keepFocusEl) {
        nextTick(() => {
            if (keepFocusEl) {
                keepFocusEl.focus()
            }
        })
    }
})

watch(() => $props.values, values => {
    displayValues = values || {} as V
    showValidation.value = false
})

watch(() => $props.errors, () => {
    showValidation.value = true
})

function onChange ({ name, value } : {name: keyof V, value: unknown}) {
    const newValues = { ...$props.values, [name]: value } as V
    displayValues = newValues
    $emit('change', newValues, {[name]: value} as V)
}

function onSubmit (data = {}) {
    const values: ValuesNotNullable = Object.assign({}, $props.values)
    Object.assign(values, data)

    if ($props.enctype === 'json') {
        $emit('submit', values)
    } else if ($props.enctype === 'formData') {
        const formData = new FormData()

        for (const [key, val] of Object.entries(values)) {
            if (val instanceof FileList || Array.isArray(val) ) {
                for (const subVal of val) {
                    formData.append(key + '[]', subVal instanceof File ? subVal : subVal.toString())
                }
            } else {
                if (val !== null && val !== undefined) {
                    formData.append(key, val.toString())
                }
            }
        }
        $emit('submitFormData', formData)
    } else {
        throw `enctype invalid: $props.enctype`
    }
}

function onReset () {
    $emit('reset')
}

function operatorName(cell: FormElement) {
    return cell.operatorName || (cell.name + ( cell.operatorPostfix || '_operator'))
}

function cellProps (cell: FormElement) {
    const result: FormElementForComponent = Object.assign({}, cell, {
        id: `${makeCellId(cell)}`,
        value: cell.name in displayValues ? displayValues[cell.name] : null,
        isInvalid: showValidation.value && !_.isEmpty(($props.errors || {})[cell.name] || ''),
    })

    if (result.size === undefined && $props.size) {
        result.size = $props.size
    }

    if (result.enabled === undefined) {
        result.enabled = $props.enabled
    }

    return result
}

function makeCellId (cell: FormElement) {
    return (cell.name && cell.title) ? `fg-${$props.uniqueId}-${cell.name}` : ''
}

function onEnter () {
    if ($props.submitOnEnter) {
        nextTick(() => onSubmit($props.values))
    }
}
</script>

<style lang="scss">
    .form-grid {
        .form-group {
            margin-bottom: 0.75rem;

            & > .label-container {
                margin-bottom: 0.25rem;

                & > label {
                    margin-bottom: 0;
                }
            }
        }

        .row > div {
            padding-left: 0.5rem;
            padding-right: 0.5rem;
        }
    }
</style>
