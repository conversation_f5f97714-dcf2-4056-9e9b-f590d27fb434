<?php

declare(strict_types=1);

return [
    '/front' => [
        '/auth' => [
            '/auth' => \app\back\modules\auth\AuthController::class,
        ],
        '/preferences' => [
            '/security' => \app\back\modules\preferences\security\SecurityController::class,
            '/settings' => \app\back\modules\preferences\settings\SettingsController::class,
        ],
        '/back' => [
            '/remarketing-sources' => \app\back\modules\back\remarketingSources\RemarketingSourcesController::class,
            '/application-groups' => \app\back\modules\back\applicationGroups\ApplicationGroupsController::class,
            '/cid-ignores' => \app\back\modules\back\cidIgnores\CidIgnoresController::class,
            '/cid-management' => \app\back\modules\back\cidManagement\CidManagementController::class,
            '/db-stats' => \app\back\modules\back\dbStats\DbStatsController::class,
            '/contacts' => \app\back\modules\back\contacts\ContactsController::class,
            '/components-demo' => \app\back\modules\back\componentsDemo\ComponentsDemoController::class,
            '/employees' => \app\back\modules\back\employees\EmployeesController::class,
            '/employees-requests' => \app\back\modules\back\employeesRequests\EmployeesRequestsController::class,
            '/games' => \app\back\modules\back\games\GamesController::class,
            '/games-vendors' => \app\back\modules\back\games\GamesVendorsController::class,
            '/games-vendors-groups' => \app\back\modules\back\games\GamesVendorsGroupsController::class,
            '/help' => \app\back\modules\back\help\HelpController::class,
            '/ignore-users' => \app\back\modules\back\ignoreUsers\IgnoreUsersController::class,
            '/logs' => \app\back\modules\back\logs\LogsController::class,
            '/operators' => \app\back\modules\back\operators\OperatorsController::class,
            '/peons-monitor' => \app\back\modules\back\peonsMonitor\PeonsMonitorController::class,
            '/redis-monitor' => \app\back\modules\back\redisMonitor\RedisMonitorController::class,
            '/roles' => \app\back\modules\back\roles\RolesController::class,
            '/s2p-projects-flags' => \app\back\modules\back\s2pProjectsFlags\S2pProjectsFlagsController::class,
            '/settings' => \app\back\modules\back\settings\SettingsController::class,
            '/sql' => \app\back\modules\back\sql\SqlController::class,
            '/tasks-dependencies' => \app\back\modules\back\tasksDependencies\TasksDependenciesController::class,
            '/tasks-gaps' => \app\back\modules\back\tasksGaps\TasksGapsController::class,
            '/tasks-queue' => \app\back\modules\back\tasksQueue\TasksQueueController::class,
            '/tasks-history' => \app\back\modules\back\tasksHistory\TasksHistoryController::class,
            '/traffic-sources' => \app\back\modules\back\trafficSources\TrafficSourcesController::class,
            '/users-statuses-normal-thresholds' => \app\back\modules\back\usersStatusesNormalThresholds\UsersStatusesNormalThresholdsController::class,
            '/users-statuses-previp-thresholds' => \app\back\modules\back\usersStatusesPrevipThresholds\UsersStatusesPrevipThresholdsController::class,
            '/users-statuses-vip-thresholds' => \app\back\modules\back\usersStatusesVipThresholds\UsersStatusesVipThresholdsController::class,
        ],
        '/reports/{reportId}' => \app\back\modules\reports\controllers\ReportController::class,
        '/api' => [
            '/client' => \app\back\modules\api\client\ClientController::class,
            '/monitor' => \app\back\modules\api\monitor\MonitorController::class,
        ],
        '/dictionaries' => [
            '/ads-credentials' => \app\back\modules\dictionaries\adsCredentials\AdsCredentialsController::class,
            '/ads-credentials-upload-fb' => \app\back\modules\dictionaries\adsCredentials\AdsCredentialsUploadFacebookController::class,
            '/ads-stats-upload-google' => \app\back\modules\dictionaries\adsCredentials\AdsStatsUploadGoogleController::class,
            '/brands' => \app\back\modules\dictionaries\brands\BrandsController::class,
            '/canonical-pay-sys' => \app\back\modules\dictionaries\canonicalPaySys\CanonicalPaySysController::class,
            '/proxies' => \app\back\modules\dictionaries\proxies\ProxiesController::class,
            '/ref-types' => \app\back\modules\dictionaries\refTypes\RefTypesController::class,
            '/sites' => \app\back\modules\dictionaries\sites\SitesController::class,
            '/wp-aff-owner-groups' => \app\back\modules\dictionaries\wpAffOwnerGroups\WpAffOwnerGroupsController::class,
            '/filtered' => \app\back\modules\dictionaries\filtered\FilteredController::class,
        ],
        '/finance' => [
            '/bonus-restriction' => \app\back\modules\finance\bonusRestriction\BonusRestrictionController::class,
            '/bonuses-manual' => \app\back\modules\finance\bonusesManual\BonusesManualController::class,
            '/documents' => \app\back\modules\finance\documents\DocumentsController::class,
            '/documents-kyc' => \app\back\modules\finance\documentsKyc\DocumentsKycController::class,
            '/documents-text' => \app\back\modules\finance\documentsText\DocumentsTextController::class,
            '/face-matching' => \app\back\modules\finance\faceMatching\FaceMatchingController::class,
            '/face-test-tool' => \app\back\modules\finance\faceTestTool\FaceTestToolController::class,
            '/face-validation' => \app\back\modules\finance\faceValidation\FaceValidationController::class,
            '/documents-tags-validation' => \app\back\modules\finance\documentsTagsValidation\DocumentsTagsValidationController::class,
            '/payments-manual' => \app\back\modules\finance\paymentsManual\PaymentsManualController::class,
            '/users' => \app\back\modules\finance\users\UsersController::class,
            '/withdrawals' => \app\back\modules\finance\withdrawals\WithdrawalsController::class,
            '/withdrawals-restrictions' => \app\back\modules\finance\withdrawalsRestrictions\WithdrawalsRestrictionsController::class,
            '/withdrawals-rules' => \app\back\modules\finance\withdrawalsAutoProcessing\WithdrawalsRulesController::class,
        ],
        '/user' => [
            '/card' => \app\back\modules\user\card\CardController::class,
            '/chats' => \app\back\modules\user\chats\ChatsController::class,
            '/chats-viewer' => \app\back\modules\user\chatsViewer\ChatsViewerController::class,
            '/manager' => \app\back\modules\user\manager\ManagerController::class,
            '/player' => [
                '/bonus-offers' => \app\back\modules\user\player\blocks\bonusOffers\BonusOffersController::class,
                '/bonuses' => \app\back\modules\user\player\blocks\bonuses\BonusesController::class,
                '/bonuses-activation' => \app\back\modules\user\player\blocks\bonusesActivation\BonusesActivationController::class,
                '/chats' => \app\back\modules\user\player\blocks\chats\ChatsController::class,
                '/comments' => \app\back\modules\user\player\blocks\comments\CommentsController::class,
                '/contacts' => \app\back\modules\user\player\blocks\contacts\ContactsController::class,
                '/customer' => \app\back\modules\user\player\blocks\customer\CustomerController::class,
                '/tickets' => \app\back\modules\user\player\blocks\tickets\TicketsController::class,
                '/transactions' => \app\back\modules\user\player\blocks\transactions\TransactionsController::class,
                '/wallets' => \app\back\modules\user\player\blocks\wallets\WalletsController::class,
                '[/{action}]' => \app\back\modules\user\player\PlayerController::class,
            ],
            '/secret-mirrors' => \app\back\modules\user\secretMirrors\SecretMirrorsController::class,
            '/spins' => \app\back\modules\user\spins\SpinsController::class,
            '/statuses-upload' => \app\back\modules\user\statusesUpload\StatusesUploadController::class,
            '/tickets' => \app\back\modules\user\tickets\TicketsController::class,
            '/timeline' => \app\back\modules\user\timeline\TimelineController::class,
        ],
        '/tools' => [
            '/cid-visualisation' => \app\back\modules\tools\cidVisualisation\CidVisualisationController::class,
            '/countries-deps-distr' => \app\back\modules\tools\countriesDepsDistr\CountriesDepsDistrController::class,
            '/domains' => \app\back\modules\tools\domains\DomainsController::class,
            '/domains-upload' => \app\back\modules\tools\domainsUpload\DomainsUploadController::class,
            '/domains-upload-registration' => \app\back\modules\tools\domainsUpload\DomainsUploadRegistrationController::class,
            '/service-accounts' => \app\back\modules\tools\serviceAccounts\ServiceAccountsController::class,
            '/service-accounts-upload' => \app\back\modules\tools\serviceAccountsUpload\ServiceAccountsUploadController::class,
            '/whats-app-contacts' => \app\back\modules\tools\whatsAppContacts\WhatsAppContactsController::class,
            '/webmasters-contacts' => \app\back\modules\tools\webmastersContacts\WebmastersContactsController::class,
        ],
        '/checks' => [
            '/checks' => \app\back\modules\checks\checks\ChecksController::class,
            '/users-checks' => \app\back\modules\checks\usersChecks\UsersChecksController::class,
            '/groups' => \app\back\modules\checks\groups\GroupsController::class,
        ],
        '/lyra' => [
            '/feature-sets' => \app\back\modules\lyra\featureSets\FeatureSetsController::class,
            '/verification' => \app\back\modules\lyra\verification\VerificationController::class,
        ],
        '/monitoring' => [
            '/prometheus' => \app\back\modules\monitoring\prometheus\PrometheusController::class,
            '/charts/panel[/{action}[/{panelId}]]' => \app\back\modules\monitoring\charts\panel\PanelController::class,
            '/charts/panel-edit' => \app\back\modules\monitoring\charts\panelEdit\PanelEditController::class,
            '/charts/chart-edit' => \app\back\modules\monitoring\charts\chartEdit\ChartEditController::class,
        ]
    ],
    '/api' => new \app\back\components\RoutesProviderApi(__DIR__ . '/routes_api.php'),
    '/events/subscriptions' => new \app\back\components\RoutesProviderRest(\app\back\modules\events\subscriptions\SubscriptionsController::class),
];
