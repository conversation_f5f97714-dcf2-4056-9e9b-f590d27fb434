<?php

declare(strict_types=1);

use app\back\config\tasks\Res;
use app\back\entities\HhsUserGameSession;
use app\back\modules\task\requests\HhsRequest;

$hhsTasks = [
    'hhs-games' => [
        'class' => \app\back\modules\task\actions\import\hhs\HhsGamesTask::class,
        'request' => [
            'class' => HhsRequest::class,
            'url' => 'Game/getList',
            'aliases' => [
                'game_id' => 'game_id',
                'game_name' => 'name',
                'absolute_name' => 'absolute_name',
                'slug' => 'slug',
            ],
        ],
    ],
    'hhs-users-games-sessions' => [
        'class' => \app\back\modules\task\actions\import\hhs\HhsUsersGamesSessionsTask::class,
        'splitPeriod' => 'PT1H',
        'request' => [
            'class' => HhsRequest::class,
            'url' => 'Statistic/getPreciseGameStatisticsByUsersCSV',
            'parserConfig' => [
                'class' => \app\back\components\parsers\CsvParser::class,
                'delimiter' => ';',
            ],
            'aliases' => [
                'tag_name' => 'tag_name',
                //'tag_id_from_feed' => 'tag_id',
                'project_id' => 'system_id',
                'is_aggregator' => 'is_aggregator',
                'game_id' => 'game_id',
                'date' => 'date',
                'currency' => 'currency',
                //'mode' => 'mode',
                'session_id' => 'session_id',
                'user_id_raw' => 'user_id',

                'bet_count' => 'slot_count',
                'bet_sum' => 'slot_bet_sum',
                'win_sum' => 'slot_win_sum',
                'bet_sum_usd' => 'slot_bet_sum_usd',
                'win_sum_usd' => 'slot_win_sum_usd',

                'double_count' => 'double_count',
                'double_bet_sum' => 'double_bet_sum',
                'double_win_sum' => 'double_win_sum',
                'double_bet_sum_usd' => 'double_bet_sum_usd',
                'double_win_sum_usd' => 'double_win_sum_usd',

                'profit_usd' => 'profit_usd',
                'created_at' => 'created',
                'updated_at' => 'modified',
            ],
        ],
    ],
    'hhs-projects' => [
        'class' => \app\back\modules\task\actions\import\hhs\HhsProjectsTask::class,
        'request' => [
            'class' => HhsRequest::class,
            'url' => 'System/getList',
            'aliases' => [
                'project_id' => 'system_id',
                'project_name' => 'name',
                'title' => 'title',
            ],
        ],
    ],
    'hhs-session-events' => [
        'class' => \app\back\modules\task\FetchTask::class,
        'request' => [
            'class' => \app\back\modules\task\requests\HhsSpinsRequest::class,
            'url' => 'Game/getEvents',
            'aliases' => [
                'event_id' => 'event_id',
                'data' => 'data',
                'time' => 'time',
            ],
        ],
    ],
];

$hhsInc = [
    'gameIdIncrement' => 0,
    'projectIdIncrement' => 0,
    'instanceId' => HhsUserGameSession::INSTANCE_HHS,
];

$hhsIncEe = [
    'gameIdIncrement' => 1000000,
    'projectIdIncrement' => 1000000,
    'instanceId' => HhsUserGameSession::INSTANCE_HHS_EE,
];

$hhsIncWhite = [
    'gameIdIncrement' => 2000000,
    'projectIdIncrement' => 2000000,
    'instanceId' => HhsUserGameSession::INSTANCE_HHS_WHITE,
];

$hhsIncPh = [
    'gameIdIncrement' => 3000000,
    'projectIdIncrement' => 3000000,
    'instanceId' => HhsUserGameSession::INSTANCE_HHS_WHITE,
];

return [
    Res::PLATFORM_HHS => $hhsTasks,
    Res::HHS_SMEN => [
        'hhs-games' => $hhsInc,
        'hhs-users-games-sessions' => $hhsInc,
        'hhs-projects' => $hhsInc
    ],
    Res::HHS_FLAME => [
        'hhs-games' => $hhsInc,
        'hhs-users-games-sessions' => $hhsInc,
        'hhs-projects' => $hhsInc
    ],
    Res::HHS_EE => [
        'hhs-games' => $hhsIncEe,
        'hhs-users-games-sessions' => $hhsIncEe,
        'hhs-projects' => $hhsIncEe
    ],
    Res::HHS_WHITE => [
        'hhs-games' => $hhsIncWhite,
        'hhs-users-games-sessions' => $hhsIncWhite,
        'hhs-projects' => $hhsIncWhite
    ],
    Res::HHS_PH => [
        'hhs-games' => $hhsIncPh,
        'hhs-users-games-sessions' => $hhsIncPh,
        'hhs-projects' => $hhsIncPh
    ],
];
