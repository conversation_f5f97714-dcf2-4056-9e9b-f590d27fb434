<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\BigIdValidator;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;

class LotteryTicket extends BaseEntity
{
    #[IdValidator]
    public int $site_id;
    #[IdValidator]
    public int $ticket_id;
    #[IdValidator]
    public int $lottery_id;
    #[BigIdValidator]
    public int $user_id;
    #[DateTimeImmutableValidator]
    public \DateTimeImmutable $created_at;
    #[BooleanValidator]
    public ?bool $is_gold;
}
