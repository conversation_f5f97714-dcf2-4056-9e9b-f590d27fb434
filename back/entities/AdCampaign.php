<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\StringValidator;

class AdCampaign extends BaseEntity
{
    public const string CAMPAIGN_NAME_EMPTY = '[empty name]';

    public const int STATUS_ACTIVE = 1;
    public const int STATUS_PAUSED = 2;
    public const int STATUS_DELETED = 3;
    public const int STATUS_ARCHIVED = 4;
    public const int STATUS_IN_PROCESS = 5;
    public const int STATUS_WITH_ISSUES = 6;
    public const int STATUS_UNKNOWN = 7;

    public const array STATUSES = [
        self::STATUS_ACTIVE => 'Active',
        self::STATUS_PAUSED => 'Paused',
        self::STATUS_DELETED => 'Deleted',
        self::STATUS_ARCHIVED => 'Archived',
        self::STATUS_IN_PROCESS => 'In process',
        self::STATUS_WITH_ISSUES => 'With issues',
        self::STATUS_UNKNOWN => 'Unknown',
    ];

    public int $id;
    #[IdValidator]
    public int $platform_id;
    #[StringValidator(1, 100)]
    public string $external_id;
    #[StringValidator(1, 500)]
    public string $name;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $created_at;
    #[IdValidator]
    public ?int $account_id;
    #[IntInArrayValidator(self::STATUSES)]
    public ?int $status;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $started_at;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $updated_at;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $stopped_at;

    public static function parseCampaignName(string $name): ?array
    {
        $alphaNum = '[a-zA-Z0-9]+';
        $regex = "#^(?:$alphaNum)_(?:[0-9]+)_(?:$alphaNum)_(?<country>[A-Z]{2})_(?:$alphaNum)_(?<platform>$alphaNum)_(.+)$#";

        if (!preg_match($regex, $name, $matches)) {
            return null;
        }

        return array_filter($matches, 'is_string', ARRAY_FILTER_USE_KEY);
    }

    public static function generateAccountSecret(string $accountId): string
    {
        $hash = hash_hmac('sha256', $accountId, $_ENV['GOOGLE_ADS_SALT'], true);
        return substr(base64_encode($hash), 0, 32);
    }
}
