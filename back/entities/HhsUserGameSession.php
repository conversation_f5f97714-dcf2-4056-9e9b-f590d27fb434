<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\helpers\Arr;
use app\back\components\validators\BigIdValidator;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\CurrencyFormatValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\DateValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\MoneyValidator;
use app\back\components\validators\StringValidator;
use app\back\config\tasks\Res;

class HhsUserGameSession extends BaseEntity
{
    public const int INSTANCE_HHS = 1;
    public const int INSTANCE_HHS_EE = 2;
    public const int INSTANCE_HHS_WHITE = 3;
    public const int INSTANCE_HHS_SMEN = 4;
    public const int INSTANCE_HHS_PH = 5;

    private const array INSTANCE_RESOURCES = [
        self::INSTANCE_HHS => Res::HHS_FLAME,
        self::INSTANCE_HHS_EE => Res::HHS_EE,
        self::INSTANCE_HHS_WHITE => Res::HHS_WHITE,
        self::INSTANCE_HHS_SMEN => Res::HHS_SMEN,
        self::INSTANCE_HHS_PH => Res::HHS_PH,
    ];

    public const int TAG_PAID = 1;
    public const int TAG_BONUS_SPINS = 2;
    public const int TAG_FREE_SPINS = 3;
    public const int TAG_TEST = 4;

    public const array TAG_NAMES = [
        self::TAG_PAID => 'Paid spins',
        self::TAG_BONUS_SPINS => 'Bonus spins',
        self::TAG_FREE_SPINS => 'Free spins',
        self::TAG_TEST => 'Test spins',
    ];

    #[DateValidator]
    public string $date;
    #[StringValidator(1, 60)]
    public string $session_id;
    #[IdValidator]
    public int $project_id;
    #[BooleanValidator]
    public bool $is_aggregator;
    #[IdValidator]
    public ?int $site_id;
    #[StringValidator(1, 255)]
    public string $user_id_raw;
    #[IdValidator]
    public int $game_id;
    #[CurrencyFormatValidator]
    public string $currency;
    #[IntValidator]
    public int $bet_count = 0;
    #[MoneyValidator]
    public string $bet_sum = '0';
    #[MoneyValidator]
    public string $win_sum = '0';
    #[MoneyValidator]
    public string $bet_sum_usd = '0';
    #[MoneyValidator]
    public string $win_sum_usd = '0';
    #[MoneyValidator]
    public string $bet_sum_rub = '0';
    #[MoneyValidator]
    public string $win_sum_rub = '0';
    #[IntValidator]
    public int $double_count = 0;
    #[MoneyValidator]
    public string $double_bet_sum = '0';
    #[MoneyValidator]
    public string $double_win_sum = '0';
    #[MoneyValidator]
    public string $double_bet_sum_usd = '0';
    #[MoneyValidator]
    public string $double_win_sum_usd = '0';
    #[MoneyValidator]
    public string $double_bet_sum_rub = '0';
    #[MoneyValidator]
    public string $double_win_sum_rub = '0';
    #[DateTimeImmutableValidator]
    public \DateTimeImmutable $created_at;
    #[DateTimeImmutableValidator]
    public \DateTimeImmutable $updated_at;
    #[MoneyValidator]
    public ?string $bet_sum_eur = '0';
    #[MoneyValidator]
    public ?string $win_sum_eur = '0';
    #[MoneyValidator]
    public ?string $double_bet_sum_eur = '0';
    #[MoneyValidator]
    public ?string $double_win_sum_eur = '0';
    #[BigIdValidator]
    public ?int $user_id;
    #[IntInArrayValidator(self::TAG_NAMES)]
    public int $tag_id;
    #[IntInArrayValidator(self::INSTANCE_RESOURCES)]
    public int $instance_id = self::INSTANCE_HHS;

    public static function getResourceByInstanceId(int $instanceId): string
    {
        return self::INSTANCE_RESOURCES[$instanceId];
    }

    public static function parseSessionId(string $sessionId): array
    {
        /* session_id:
            1) агрегатор(1) / старый тип (0)
            2) demo-игра (1) / не-демо (0) - FUN будет считаться не-демо
            3) id сессии (юзера hhs) (не всегда уникальный)
            4) window - счётчик окон начиная с 1 в рамках "id сессии"
        */

        preg_match('#^(?<aggregator>[01])-(?<demo>[01])-(?<user_id>[-\w]+)-(?<window_id>\d+)$#', $sessionId, $matches);

        return Arr::leaveOnlyKeys($matches, ['aggregator', 'demo', 'user_id', 'window_id']);
    }
}
