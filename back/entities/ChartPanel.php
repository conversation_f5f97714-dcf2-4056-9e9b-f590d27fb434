<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\JsonObjectValidator;
use app\back\components\validators\StringValidator;
use app\back\modules\monitoring\charts\models\Chart;
use app\back\modules\monitoring\charts\models\Serie;
use app\back\modules\monitoring\charts\sources\BaseChartSource;

class ChartPanel extends BaseEntity
{
    #[IdValidator]
    public int $id;
    #[StringValidator(1, 100)]
    public ?string $name;
    #[JsonObjectValidator]
    public array $config = [];
    #[IntValidator]
    public ?int $created_by;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $created_at;

    /** @return Chart[][] */
    public function charts(): array
    {
        $result = [];
        foreach ($this->config['charts'] as $row => $cols) {
            foreach ($cols as $col => $chartConfig) {
                $result[$row][$col] = Chart::fromConfig($chartConfig);
            }
        }

        return $result;
    }

    public function chart(int $row, int $col): Chart
    {
        return $this->charts()[$row][$col] ?? new Chart();
    }

    public function updatedConfigChart(int $row, int $col, array $chartConfig): array
    {
        $config = $this->config;
        $series = $config['charts'][$row][$col]['series'] ?? $chartConfig['series'] ?? [];
        $config['charts'][$row][$col] = array_merge($chartConfig, [
            'series' => $series,
        ]);

        return $config;
    }

    public function updatedChartSerieConfig(int $row, int $col, ?int $serieIndex, ?Serie $serie): array
    {
        $config = $this->config;

        if ($serie === null) {
            unset($config['charts'][$row][$col]['series'][$serieIndex]);
            $config['charts'][$row][$col]['series'] = array_values($config['charts'][$row][$col]['series']);
        } elseif ($serieIndex === null) {
            $config['charts'][$row][$col]['series'][] = $serie->getAttributes();
        } else {
            $config['charts'][$row][$col]['series'][$serieIndex] = $serie->getAttributes();
        }

        return $config;
    }

    public function updatedChartSerieSourceConfig(int $row, int $col, int $serieIndex, BaseChartSource $source): array
    {
        $config = $this->config;
        $config['charts'][$row][$col]['series'][$serieIndex]['sourceConfig'] = $source->getAttributes();
        return  $config;
    }

    public static function defaultChartPanelConfig(): array
    {
        return [
            'rows' => 1,
            'cols' => 2,
            'charts' => [[new Chart()]]
        ];
    }
}
