<?php

declare(strict_types=1);

namespace app\back\components\validators;

use app\back\entities\Employee;

#[\Attribute]
class PasswordValidator extends BaseValidator
{
    public function __construct(
        protected string $employeePropName,
        protected string $errorMessage = 'is incorrect'
    ) {
    }

    public function validate(mixed $value, $form, array $context): null|string
    {
        /* @var $employee Employee */
        $employee = $form->{$this->employeePropName};

        return !preg_match('/^\$2[axy]\$(\d\d)\$[\.\/\dA-Za-z]{22}/', $employee->password, $matches)
        || $matches[1] < 4
        || $matches[1] > 30
        || !password_verify($value, $employee->password) ? $this->errorMessage : null;
    }
}
