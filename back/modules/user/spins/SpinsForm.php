<?php

declare(strict_types=1);

namespace app\back\modules\user\spins;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\exceptions\InvalidException;
use app\back\components\FormGrid;
use app\back\components\helpers\Arr;
use app\back\components\helpers\DateHelper;
use app\back\components\helpers\Url;
use app\back\components\ResponseCsv;
use app\back\components\SecondaryConnection;
use app\back\components\SessionMessages;
use app\back\components\validators\AllowedSiteValidator;
use app\back\components\validators\BaseValidator;
use app\back\components\validators\CallableValidator;
use app\back\components\validators\DateTimeValidator;
use app\back\components\validators\NestedValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\components\validators\StringMultilineValidator;
use app\back\components\validators\StringValidator;
use app\back\components\validators\UserIdOrSiteUserIdValidator;
use app\back\components\validators\UuidValidator;
use app\back\entities\HhsUserGameSession;
use app\back\entities\Site;
use app\back\entities\UserTransaction;
use app\back\modules\monitoring\charts\models\Serie;
use app\back\modules\task\components\FetchTaskFactory;
use app\back\repositories\Games;
use app\back\repositories\HhsGames;
use app\back\repositories\HhsProjects;
use app\back\repositories\HhsUserGameSessions;
use app\back\repositories\Sites;
use app\back\repositories\UserGameRaws;
use app\back\repositories\UserTransactions;
use Yiisoft\Db\Query\Query;

class SpinsForm
{
    use FormGrid;

    private const int LIMIT_TABLE_ROWS = 30000;
    public const string PRODUCT_VALID_PERIOD = '1 month';

    public const string SOURCE_PRODUCT = 'product';
    public const string SOURCE_HHS = 'hhs';

    public const array SOURCES = [
        self::SOURCE_PRODUCT => 'Product',
        self::SOURCE_HHS => 'HHS',
    ];

    #[AllowedSiteValidator]
    public ?int $siteId = null;
    #[UserIdOrSiteUserIdValidator]
    #[CallableValidator([self::class, 'allowedProductSites'])]
    public string $userId;
    #[StringInArrayValidator(self::SOURCES)]
    public string $source = self::SOURCE_PRODUCT;
    #[NestedValidator([self::class, 'sessionIdValidatorBySource'])]
    public ?string $sessionId = null;
    #[DateTimeValidator]
    #[CallableValidator([self::class, 'checkForm'])]
    public string $from;
    #[DateTimeValidator]
    public ?string $to = null;
    #[StringMultilineValidator(0, 100)]
    public ?string $game = null;

    private array $siteIdUserId;

    public function __construct(
        private readonly SecondaryConnection $db,
        private readonly HhsGames $hhsGamesRepo,
        private readonly HhsProjects $hhsProjectsRepo,
        private readonly Sites $sitesRepo,
        public readonly AllowedLists $allowedLists,
        private readonly FetchTaskFactory $requestFactory,
        private readonly BaseAuthAccess $auth,
        private readonly SessionMessages $SessionMessages,
    ) {
        $this->from = date(DateHelper::DATE_FORMAT_PHP, strtotime('-' . self::PRODUCT_VALID_PERIOD));
    }

    public static function allowedProductSites(mixed $value, self $form): ?string
    {
        if ($value === null) {
            return null;
        }

        $siteIdUserId = UserIdOrSiteUserIdValidator::siteIdUserIdFromValue($value, $form);

        if (!in_array($siteIdUserId['site_id'], array_merge(Site::PLATFORM_SITES_SMEN, Site::PLATFORM_SITES_GI), true)) {
            return "This site doesn't provide spins data";
        }
        $form->siteId = $siteIdUserId['site_id'];

        return null;
    }

    public static function checkForm(string $value, self $form): ?string
    {
        if ($value < date(DateHelper::DATE_FORMAT_PHP, strtotime('-' . self::PRODUCT_VALID_PERIOD)) && $form->source === self::SOURCE_PRODUCT) {
            return "Can't show spins log for more than " . self::PRODUCT_VALID_PERIOD . " ago";
        }

        return null;
    }

    public static function sessionIdValidatorBySource(mixed $value, self $form, array $context): BaseValidator
    {
        return (isset($context['source']) && $context['source'] === self::SOURCE_PRODUCT) ? new UuidValidator() : new StringValidator(1, 60);
    }

    public static function userHref(int $siteId, int $userId, string $source): string
    {
        return Url::to('/user/spins', [
            'siteId' => $siteId,
            'userId' => $userId,
            'source' => $source,
        ]);
    }

    public static function columns(): array
    {
        return [
            ['name' => 'Game', 'code' => 'game'],
            ['name' => 'Action', 'code' => 'action', 'sortable' => true],
            ['name' => 'RoundType', 'code' => 'roundType', 'sortable' => true],
            ['name' => 'Date', 'code' => 'date', 'sortable' => true],
            ['name' => 'All Balance', 'code' => 'balance'],
            ['name' => 'Real Balance', 'code' => 'realBalance'],
            ['name' => 'Bonus Balance', 'code' => 'bonusBalance'],
            ['name' => 'Wager Target', 'code' => 'bonusRefundSum'],
            ['name' => 'Wager Current', 'code' => 'bonusBetSum'],
            ['name' => 'Balance Type', 'code' => 'balanceType'],
            ['name' => 'Bet', 'code' => 'bet'],
            ['name' => 'Win', 'code' => 'win'],
            ['name' => 'Event ID', 'code' => 'eventId', 'slotName' => 'eventId'],
            ['name' => 'Session ID', 'code' => 'sessionId', 'slotName' => 'sessionId'],
        ];
    }

    protected function blocks(): array
    {
        return [
            [
                $this->dateTimeCell(1, 'from', 'From', [
                    'submitOnEnter' => true,
                ]),
                $this->dateTimeCell(1, 'to', 'To', [
                    'submitOnEnter' => true,
                    'buttonsMode' => 'end'
                ]),
                $this->selectSiteCell(2, 'siteId', 'Site', [
                    'multiple' => false,
                ]),
                $this->textInputCell(2, 'userId', 'User Id / Site-User Id', [
                    'submitOnEnter' => true,
                ]),
                $this->textInputCell(1, 'sessionId', 'Session ID', [
                    'submitOnEnter' => true,
                ]),
                $this->textAreaCell(1, 'game', 'Game name'),
                $this->listCell(2, 'source', 'Source', [
                    'list' => Arr::assocToIdName(self::SOURCES),
                    'multiple' => false,
                ]),
                $this->submitCell(1, 'Search'),
            ],
        ];
    }

    public function data($allRows = false): array
    {
        ['site_id' => $siteId, 'user_id' => $userId] = UserIdOrSiteUserIdValidator::siteIdUserIdFromValue($this->userId, $this);
        $from = date('Y-m-d H:i:s', strtotime($this->from ?? '-7 day'));
        $to = date('Y-m-d H:i:s', strtotime($this->to ?? 'now'));

        $hhsCustomInfo = [];
        if ($this->source === static::SOURCE_HHS) {
            $data = $this->getDataFromHhs($siteId, $userId, $from, $to, $this->sessionId);

            $hhsCustomInfo = [
                'userId' => $userId,
                'hhsProjectName' => $this->hhsProjectsRepo->findOne(['site_id' => $siteId])?->project_name,
            ];
        }

        if (in_array($siteId, Site::PLATFORM_SITES_GI, true)) {
            $data = $this->getDataFromUsersGamesRaw($siteId, $userId, $from, $to, $this->sessionId);
        }

        if (in_array($siteId, Site::PLATFORM_SITES_SMEN, true)) {
            $data = $this->getDataFromProduct($siteId, $userId, $from, $to);
        }

        if (!isset($data)) {
            throw new InvalidException("Can't find data source for spins load");
        }

        usort($data, static fn($a, $b) => $a['launchAtMicro'] <=> $b['launchAtMicro']);

        $lastGame = false;
        $balances = [];
        $balancesBonus = [];
        $wager = [];
        $games = [];
        $num = 1;
        foreach ($data as $item) {
            if (!isset($item['roundType']) || in_array($item['roundType'], ['spin', 'collect'], true)) {
                $item['roundType'] = null;
            }

            $time = (int) $item['launchAtMicro'] * 1000;
            // chart cannot show two actions that appear in the same time
            while (isset($balances[$time])) {
                $time++;
            }
            if (($this->game && $item['game'] === $this->game) || !$this->game) {
                $balances[$time] = [
                    'x' => $time,
                    'y' => (int)$item['balance'],
                    'action' => $item['action'],
                    'date' => date('Y-m-d H:i:s', (int)$item['launchAtMicro']),
                    'balance' => $item['balance'],
                    'realBalance' => $item['realBalance'],
                    'bonusBalance' => $item['bonusBalance'],
                    'bonusRefundSum' => $item['bonusRefundSum'] ?? null,
                    'bonusBetSum' => $item['bonusBetSum'] ?? null,
                    'game' => $item['game'],
                    'bet' => $item['action'] === 'bet' ? $item['amount'] : null,
                    'win' => $item['action'] === 'win' ? $item['amount'] : null,
                    'roundType' => $item['roundType'],
                    'balanceType' => $item['balanceType'] ?? null,
                    'eventId' => $item['actionId'] ?? null,
                    'symbols' => $item['symbols'] ?? null,
                    'sessionId' => $item['session_token'] ?? null,
                    ...$hhsCustomInfo,
                ];

                if ($item['bonusBalance'] !== null) {
                    $balancesBonus[$time] = [
                        'x' => $time,
                        'y' => (int)$item['bonusBalance'],
                    ];
                }

                if (isset($item['bonusBetSum'])) {
                    $wager[$time] = [
                        'x' => $time,
                        'y' => (int)$item['bonusBetSum'],
                    ];
                }

                if ($item['game'] !== $lastGame) {
                    $lastGame = $item['game'];
                    $games[] = [
                        'x' => $time,
                        'y' => (int)$item['balance'],
                        'title' => $item['game'],
                    ];
                }

                if ($num >= self::LIMIT_TABLE_ROWS && !$allRows) {
                    $this->SessionMessages->warning('Result too big and limited to ' . self::LIMIT_TABLE_ROWS . ' rows!');
                    break;
                }
                $num++;
            }
        }

        $seriesData = [
            [array_values($balances), 'Balance', 'teal'],
            [array_values($balancesBonus), 'Bonus Balance', 'blue'],
            [array_values($wager), 'Wager', 'warning'],
        ];

        foreach ($seriesData as [$serieData, $name, $color]) {
            [$series[], $yAxis[]] = $this->getSerieAndYaxis($serieData, $name, $color);
        }

        $series[] = [
            'type' => 'flags',
            'data' => $games,
        ];

        return [
            'series' => $series,
            'yAxis' => $yAxis,
            'plotLinesValues' => [
            ...self::getPlotLinesValuesSettings($this->getPayments($siteId, $userId, $from, $to), 'fu-maroon'),
            ],
        ];
    }

    public function download(): ResponseCsv
    {
        $columns = Arr::map(self::columns(), 'code', 'name');
        $rows = $this->data(true)['series'][0]['data'];
        return new ResponseCsv($rows, $columns, $this->auth->employee()->getSettingsObject()->getSeparator());
    }

    private function getDataFromProduct(int $siteId, int $userId, string $from, string $to): array
    {
        $data = [];
        foreach (DateHelper::splitPeriod($from, $to, 'P1D', true) as [$fromChunk, $toChunk]) {
            $fetchTask = $this->requestFactory->createFetchTask('get-user-spins', $siteId, [
                'from' => $fromChunk,
                'to' => $toChunk,
                'userId' => $userId,
            ]);

            $data[] = Arr::fromIterable($fetchTask->finalData());
        }

        return array_merge(...$data);
    }

    private function getDataFromHhs(int $siteId, int $userId, string $from, string $to, ?string $sessionId): array
    {
        $condition = [
            'AND',
            ['site_id' => $siteId],
            ['user_id' => $userId],
            ['>=', 'updated_at', $from],
            ['<', 'updated_at', $to],
        ];

        if (!empty($sessionId)) {
            if (preg_match('/^\d+$/', $sessionId)) {
                $sessionId = "1-0-{$sessionId}-1";
            }

            $condition[] = ['session_id' => $sessionId];
        }

        $sessions = (new Query($this->db))
            ->select([
                'session_id',
                'instance_id',
            ])
            ->from(HhsUserGameSessions::TABLE_NAME)
            ->where($condition)
            ->all();

        $collected = [];
        foreach ($sessions as $session) {
            $fetchTask = $this->requestFactory->createFetchTask('hhs-session-events', HhsUserGameSession::getResourceByInstanceId($session['instance_id']), [
                'from' => $from,
                'to' => $to,
                'sessionId' => $session['session_id'],
            ]);

            foreach ($fetchTask->finalData() as $event) {
                $balance = !empty($event['data']['balance']) ? $event['data']['balance'] : $event['data']['user']['balance'];

                $sessionId = $session['session_id'];
                if (preg_match('/1-0-(\d+)-1/', $sessionId, $matches)) {
                    $sessionId = $matches[1];
                }

                $game = $this->hhsGamesRepo->getNameById($event['data']['game']['game_id']) ?? $event['data']['game']['absolute_name'];
                $sessionToken = $event['data']['game']['action'] === 'init' ? $sessionId : null;
                $roundType = in_array($event['data']['game']['action'], ['spin', 'collect', 'win'], true) ? null : $event['data']['game']['action'];

                $collected[] = [
                    'actionId' => $event['event_id'],
                    'session_token' => $sessionToken,
                    'launchAtMicro' => $event['time'],
                    'balance' => $balance,
                    'amount' => $event['data']['total_bet'] ?? null,
                    'action' => in_array($event['data']['game']['action'], ['spin', 'freespin']) ? 'bet' : $event['data']['game']['action'],
                    'game' => $game,
                    'game_id' => $event['data']['game']['game_id'],
                    'roundType' => $roundType
                ];
                if (!empty($event['data']['total_win'])) {
                    $collected[] = [
                        'actionId' => $event['event_id'],
                        'session_token' => $sessionToken,
                        'launchAtMicro' => $event['time'],
                        'balance' => $balance,
                        'amount' => $event['data']['total_win'],
                        'action' => 'win',
                        'game' => $game,
                        'game_id' => $event['data']['game']['game_id'],
                        'roundType' => $roundType,
                    ];
                }
            }
        }

        return $collected;
    }

    private function getDataFromUsersGamesRaw(int $siteId, int $userId, string $from, string $to, ?string $sessionId): array
    {
        $query = (new Query($this->db))
            ->select([
                'launchAtMicro' => 'EXTRACT(EPOCH FROM ugr.created_at)',
                'action' => 'ugr.type',
                'ugr.real_amount',
                'balance' => '(ugr.real_amount_after + ugr.bonus_amount_after)',
                'amount' => '(ugr.real_amount + ugr.bonus_amount)',
                'ugr.bonus_amount',
                'realBalance' => 'ugr.real_amount_after',
                'bonusBalance' => 'ugr.bonus_amount_after',
                'roundType' => 'ugr.round_type',
                'actionId' => 'ugr.action_id',
                'ugr.session_token',
                'game' => 'g.name',
            ])
            ->from(['ugr' => UserGameRaws::TABLE_NAME])
            ->leftJoin(['g' => Games::TABLE_NAME], 'g.id = ugr.game_id')
            ->where([
                'AND',
                ['ugr.site_id' => $siteId],
                ['ugr.user_id' => $userId],
                ['>=', 'ugr.created_at', $from],
                ['<=', 'ugr.created_at', $to],
            ])
            ->andFilterWhere(['ugr.session_token' => $sessionId])
            ->orderBy(['ugr.created_at' => SORT_ASC, 'ugr.type' => SORT_ASC]);

        $result = [];
        foreach ($query->each() as $item) {
            $balanceType = [];
            if ($item['real_amount'] > 0) {
                $balanceType[] = 'Real';
            }
            if ($item['bonus_amount'] > 0) {
                $balanceType[] = 'Bonus';
            }

            $item['balanceType'] = implode(', ', $balanceType);

            $result[] = $item;
        }

        return  $result;
    }

    private function getSerieAndYaxis(array $serieData, string $name, string $color): array
    {
        static $serieIndex = 0;

        $serie = [
            ...$this->getSerieSettings(),
            'className' => Serie::CSS_COLORS_PREFIX . $color,
            'yAxis' => $serieIndex,
            'data' => $serieData,
            'name' => $name,
        ];

        $yAxis = [
            'className' => Serie::CSS_COLORS_PREFIX . $color,
            'title' => [
                'text' => $name,
            ],
            'lineWidth' => 1,
        ];

        $serieIndex++;
        return [$serie, $yAxis];
    }

    private function getSerieSettings(): array
    {
         return [
            'turboThreshold' => 0,
            'type' => 'line',
            'marker' => [
                'enabled' => true,
                'radius' => 0,
            ],
            'shadow' => true,
            'tooltip' => [
                'valueDecimals' => 2,
            ],
         ];
    }

    private static function getPlotLinesValuesSettings(array $values, string $color): array
    {
        $result = [];
        foreach ($values as $time => $text) {
            $className = Serie::CSS_COLORS_PREFIX . $color;
            $result[] = [
                'className' => $className,
                'width' => '1',
                'value' => $time * 1000,
                'label' => [
                    'text' => $text,
                    'className' => $className,
                ],
            ];
        }

        return $result;
    }

    private function getPayments(int $siteId, int $userId, string $from, string $to): array
    {
        $data = (new Query($this->db))
            ->select([
                'us.op_id',
                'updated_at' => 'EXTRACT(EPOCH FROM us.updated_at)',
                'us.amount_orig',
            ])
            ->from(['us' => UserTransactions::TABLE_NAME])
            ->where([
                'AND',
                [
                    'us.dir' => UserTransaction::DIR_IN,
                    'us.status' => UserTransaction::STATUS_SUCCESS,
                    'us.site_id' => $siteId,
                    'us.user_id' => $userId,
                ],
                ['>=', 'us.updated_at', $from],
                ['<', 'us.updated_at', $to],
            ])
            ->all();

        $function = static fn($row) => [$row['updated_at'] => UserTransaction::OPERATIONS[$row['op_id']] . " ({$row['amount_orig']})"];
        return $this->makeKeyValueArray($function, $data);
    }

    private function makeKeyValueArray(callable $function, array $data): array
    {
        return array_merge(...array_map($function, $data));
    }
}
