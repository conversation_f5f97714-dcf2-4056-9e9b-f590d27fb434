<?php

declare(strict_types=1);

namespace app\back\modules\user\tickets;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\FormGrid;
use app\back\components\helpers\DateHelper;
use app\back\components\SecondaryConnection;
use app\back\components\services\FileStorage;
use app\back\components\validators\AllowedSiteValidator;
use app\back\components\validators\BigIdValidator;
use app\back\components\validators\FileValidator;
use app\back\components\validators\MoneyValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\UserTicket;
use app\back\entities\UserTicketFile;
use app\back\modules\task\actions\import\UsersTicketsFilesBaseTask;
use app\back\modules\task\actions\import\UsersTicketsSmenTask;
use app\back\repositories\UserTicketFiles;
use app\back\repositories\UserTransactions;
use app\back\repositories\UserTickets;
use app\back\repositories\UserTicketLogs;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class TicketCreateForm
{
    use FormGrid;

    #[AllowedSiteValidator]
    public int $siteId;
    #[BigIdValidator]
    public int $userId;
    #[StringValidator]
    public string $transId;
    #[MoneyValidator]
    public string $userAmount;
    #[FileValidator([self::class, 'allowedMimeTypes'], 100 * 1024 * 1024, 5)]
    public array $files;

    public function __construct(
        public readonly AllowedLists $allowedLists,
        private readonly SecondaryConnection $db,
        private readonly BaseAuthAccess $auth,
        private readonly UserTickets $userTicketsRepo,
        private readonly UserTicketLogs $userTicketsLogsRepo,
        private readonly UserTicketFiles $userTicketFilesRepo,
        private readonly UserTransactions $userTransactionsRepo,
        private readonly FileStorage $storage,
    ) {
    }

    protected function blocks(): array
    {
        return [
            [$this->textInputCell(12, 'transId', 'Trans id')],
            [$this->textInputCell(12, 'userAmount', 'User amount')],
            [$this->fileCell(12, 'files', 'Files', [
                'multiple' => true,
                'hint' => implode(', ', UsersTicketsFilesBaseTask::MIME_TYPES_ALLOWED),
            ])],
            [$this->submitCell(3, 'Create', ['buttonStyle' => 'btn-success'])],
        ];
    }

    public function create(): void
    {
        $this->userTransactionsRepo->findOneOr404([
            'transaction_id' => $this->transId,
            'site_id' => $this->siteId,
            'user_id' => $this->userId,
        ]);

        /** @var UserTicket $ticket */
        $ticket = $this->userTicketsRepo->validateAndCreate([
            'site_id' => $this->siteId,
            'user_id' => $this->userId,
            'type' => UserTicket::TYPE_LOST_DEPOSIT,
            'status' => UserTicket::STATUS_NEED_APPROVE,
            'source' => UserTicket::SOURCE_ANALYTIC,
            'invoice_id' => $this->transId,
            'amount_from_user' => $this->userAmount,
        ]);

        $this->userTicketsRepo->insert($ticket);

        $log = $this->userTicketsLogsRepo->validateAndCreate([
            'ticket_id' => $ticket->id,
            'status' => $ticket->status,
            'source' => UserTicket::SOURCE_ANALYTIC,
            'created_by' => $this->auth->employee()->email,
            'created_at' => date(DateHelper::DATETIME_FORMAT_PHP),
        ]);

        $this->userTicketsLogsRepo->insert($log);

        foreach ($this->files as $uploadedFile) {
            /** @var UploadedFile $uploadedFile
             * @var UserTicketFile $file */
            $file = $this->userTicketFilesRepo->validateAndCreate([
                'ticket_id' => $ticket->id,
                'original_name' => $uploadedFile->getClientOriginalName(),
                'extension' => UsersTicketsSmenTask::ALLOWED_EXTENSIONS_MAP[$uploadedFile->getClientOriginalExtension()],
                'sync_status' => UserTicketFile::SYNC_SUCCESS,
                'source' => UserTicket::SOURCE_ANALYTIC,
            ]);
            $this->userTicketFilesRepo->insert($file);

            $path = $this->userTicketFilesRepo->getStoragePath($file->id, $file->extension);
            $this->storage->put($path, $uploadedFile->getContent(), $uploadedFile->getMimeType(), []);
        }
    }

    public static function allowedMimeTypes(): array
    {
        return array_keys(UsersTicketsFilesBaseTask::MIME_TYPES_ALLOWED);
    }
}
