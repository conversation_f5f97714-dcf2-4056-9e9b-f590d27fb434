<?php

declare(strict_types=1);

namespace app\back\modules\back\help;

use app\back\components\helpers\Str;
use app\back\components\RichTable;
use app\back\components\validators\DateValidator;
use app\back\components\validators\IntArrayValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\Employee;
use app\back\repositories\Employees;
use app\back\repositories\HelpPages;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class HelpFilterForm
{
    use RichTable;

    #[StringValidator(0, 250)]
    public ?string $url = null;
    #[DateValidator]
    public ?string $updated_from = null;
    #[DateValidator]
    public ?string $updated_to = null;
    #[IntArrayValidator]
    public ?array $editor = null;

    public function __construct(
        private readonly ConnectionInterface $db
    ) {
    }

    protected function columns(array $context): array
    {
        return [
            ['name' => 'Url', 'code' => 'url', 'align' => 'start'],
            ['name' => 'Description', 'code' => 'description'],
            ['name' => 'Elements', 'slotName' => 'elements'],
            ['name' => 'Last Editor', 'code' => 'employee'],
            ['name' => 'Updated At', 'code' => 'updated_at'],
            ['name' => 'Actions', 'slotName' => 'actions'],
        ];
    }

    protected function blocks(): array
    {
        $editorsQuery = (new Query($this->db))
            ->select([
                'id' => 'h.last_editor_id',
                'name' => 'e.email',
                'count' => 'COUNT(*)',
            ])
            ->from(['h' => HelpPages::TABLE_NAME])
            ->leftJoin(['e' => Employees::TABLE_NAME], 'e.employee_id = h.last_editor_id')
            ->groupBy(['h.last_editor_id', 'e.email'])
            ->orderBy(['h.last_editor_id' => SORT_ASC]);

        $editors = $this->applyFilters($editorsQuery, 'editors')->all();

        return [
            [
                $this->textInputCell(3, 'url', 'Url'),
                $this->dateCell(1, 'updated_from', 'Updated From'),
                $this->dateCell(1, 'updated_to', 'Updated To'),
                $this->listCell(7, 'editor', 'Last editor', [ 'list' => $editors])
            ],
        ];
    }

    public function data(): array
    {
        $helpsQuery = (new Query($this->db))
            ->select([
                'id',
                'url',
                'description' => "(CASE WHEN LENGTH(h.description) > 100 THEN LEFT(h.description, 150) || '...'  ELSE h.description END)",
                'elements',
                'employee' => 'e.email',
                'updated_at',
            ])
            ->from(['h' => HelpPages::TABLE_NAME])
            ->leftJoin(['e' => Employees::TABLE_NAME], 'e.employee_id = h.last_editor_id')
            ->orderBy(['updated_at' => SORT_DESC])
            ->offset($this->getOffset())
            ->limit($this->getLimit());

        $data = $this->applyFilters($helpsQuery)->all();

        foreach ($data as &$datum) {
            $datum['employee'] = Str::emailToLdap($datum['employee']);
            $datum['elements'] = array_keys(json_decode($datum['elements'], true, 512, JSON_THROW_ON_ERROR));
        }
        return $data;
    }

    protected function total(): int
    {
        $total = (new Query($this->db))
            ->select('COUNT(*)')
            ->from(['h' => HelpPages::TABLE_NAME])
            ->leftJoin(['e' => Employees::TABLE_NAME], 'e.employee_id = h.last_editor_id');

        $this->applyFilters($total);

        return (int) $total->scalar();
    }

    private function applyFilters(Query $query, string $exclusion = ''): Query
    {
        $query
            ->andFilterWhere(['ilike', 'url', $this->url])
            ->andFilterWhere(['>=', 'updated_at', $this->updated_from])
            ->andFilterWhere(['<', 'updated_at', $this->updated_to]);

        if ($exclusion !== 'editors') {
            $query->andFilterWhere(['h.last_editor_id' => $this->editor]);
        }

        return $query;
    }
}
