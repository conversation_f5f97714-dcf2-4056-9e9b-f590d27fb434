<?php

declare(strict_types=1);

namespace app\back\modules\back\componentsDemo;

use app\back\components\FormGrid;
use app\back\components\helpers\Arr;
use app\back\components\helpers\DateHelper;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\DateTimeValidator;
use app\back\components\validators\DateValidator;
use app\back\components\validators\MatchValidator;
use app\back\components\validators\StringValidator;
use app\back\components\validators\TimeValidator;
use app\back\modules\monitoring\charts\models\Serie;

class DemoFormGridForm
{
    use FormGrid;

    #[DateValidator]
    private ?string $date = '2023-12-15';
    #[DateTimeValidator]
    private ?string $dateTime;
    #[StringValidator]
    #[MatchValidator(DateHelper::RANGE_PATTERN)]
    private ?string $dateRange;
    #[BooleanValidator]
    private bool $yesOrNo;
    #[TimeValidator]
    private string $time;
    #[BooleanValidator]
    private bool $checkbox;
    #[StringValidator]
    private string $mdtext = <<<MD
## Heading level 2 
### Heading level 3
Love**is**bold 
A*cat*meow 
> This is a blockquote
1. First item
2. Second item 
---
- First item
- Second item
- Third item

[link](https://www.example.com)

``Use code in your Markdown file.``
MD;

    protected function blocks(): array
    {
        $list = Arr::assocToIdName(get_loaded_extensions());
        return [
            [
                $this->dateCell(2, 'date', 'Date'),
                $this->dateTimeCell(2, 'dateTime', 'DateTime'),
                $this->dateRangeCall(2, 'dateRange', 'DateRange'),
                $this->selectBooleanCell(2, 'yesOrNo', 'Boolean'),
                $this->timeCell(2, 'time', 'Time'),
                $this->textStaticCell(2, 'Static text', 'Static title'),
            ],
            [
                $this->textInputCell(2, 'input', 'Input'),
                $this->textAreaCell(2, 'text', 'Text area'),
                $this->slotCell(2, 'customCell', 'Slot cell', ['customProp' => 'customValue']),
                $this->submitCell(2, 'Submit button'),
                $this->submitCell(2, 'Submit button with icon', [
                    'buttonIcon' => 'icn-plus',
                ]),
                $this->checkboxCell(1, 'checkbox', 'Checkbox'),
                $this->fileCell(3, 'file', 'File'),
            ],
            [
                $this->radioListCell(2, 'radio', 'Radio List', [
                    'list' => array_slice($list, 0, 5, true),
                ]),
                $this->selectCell(2, 'select', 'Select', [
                    'list' => $list,
                ]),
                $this->listCell(8, 'list', 'List', [
                    'list' => $list,
                ]),
            ],
            [
                $this->colorListCell(12, 'color', 'Color', [
                    'list' => Arr::assocToIdName(Serie::COLORS),
                ]),
            ],
            [
                $this->mdEditorCell(8, 'mdtext', 'MD editor', 5),
            ],
        ];
    }
}
