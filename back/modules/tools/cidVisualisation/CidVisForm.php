<?php

declare(strict_types=1);

namespace app\back\modules\tools\cidVisualisation;

use app\back\components\AllowedLists;
use app\back\components\exceptions\InvalidException;
use app\back\components\exceptions\NotFoundException;
use app\back\components\FormGrid;
use app\back\components\helpers\Arr;
use app\back\components\SecondaryConnection;
use app\back\components\InBuilder;
use app\back\components\SiteUserBuilder;
use app\back\components\validators\AllowedSiteValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\RequiredWhenAllEmptyValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\components\validators\StringValidator;
use app\back\components\validators\UserIdOrSiteUserIdMultilineValidator;
use app\back\entities\UserCloudSource;
use app\back\repositories\Sites;
use app\back\repositories\UserCloudSources;
use app\back\repositories\Users;
use Yiisoft\Db\Query\Query;

class CidVisForm
{
    use FormGrid;

    private const string MODE_DEFAULT = 'default';
    private const string MODE_CLUSTERS = 'clusters';
    public const array MODES = [CidVisForm::MODE_DEFAULT, CidVisForm::MODE_CLUSTERS];

    private const int MAX_CLOUD_SIZE = 100000;

    #[IdValidator]
    #[RequiredWhenAllEmptyValidator(['requisite', 'siteUser'])]
    public ?int $cid = null;
    #[StringValidator]
    #[RequiredWhenAllEmptyValidator(['cid', 'siteUser'])]
    public ?string $requisite = null;
    #[AllowedSiteValidator]
    public ?int $siteId = null;
    #[UserIdOrSiteUserIdMultilineValidator]
    #[RequiredWhenAllEmptyValidator(['cid', 'requisite'])]
    public ?string $siteUser = null;
    #[StringInArrayValidator(self::MODES, true)]
    public ?string $mode = self::MODE_DEFAULT;

    private array $foundCid;

    private readonly SiteUserBuilder $siteUserBuilder;

    public function __construct(
        private readonly CidVisResponse $response,
        private readonly SecondaryConnection $db,
        private readonly Sites $sites,
        public readonly AllowedLists $allowedLists,
    ) {
    }

    public function formName(): string
    {
        return 'cv';
    }

    protected function blocks(): array
    {
        return [
            [
                $this->listCell(2, 'mode', '', [
                    'list' => Arr::assocToIdName([self::MODE_CLUSTERS => 'Clusters']),
                    'multiple' => false,
                ]),
                $this->textInputCell(2, 'cid', '', [
                    'placeholder' => 'CID',
                    'submitOnCtrlEnter' => true,
                ]),
                $this->textInputCell(2, 'requisite', '', [
                    'placeholder' => 'Requisite',
                    'submitOnCtrlEnter' => true,
                ]),
                $this->selectSiteCell(2, 'siteId', '', [
                    'placeholder' => 'Site',
                    'multiple' => false,
                ]),
                $this->textAreaCell(2, 'siteUser', '', [
                    'placeholder' => 'User IDs or Site-User IDs',
                    'rows' => 1,
                    'submitOnCtrlEnter' => true,
                ]),
                $this->submitCell(2, 'Submit'),
            ],
        ];
    }

    public function data(): array
    {
        $foundCid = $this->findCidOr404();
        if ($this->mode === self::MODE_CLUSTERS) {
            return $this->response->cloudModeClustered($foundCid, $this->searchFilter());
        }

        $this->checkCloudSizeLimit($foundCid);
        return $this->response->cloudModeDefault($foundCid);
    }

    public function stat(): array
    {
        return $this->response->stat($this->findCidOr404());
    }

    private function findCidOr404(): array
    {
        $query = (new Query($this->db))
            ->select('u.cid', 'DISTINCT')
            ->from(['u' => Users::TABLE_NAME])
            ->where(['IS NOT', 'u.cid', null]);

        if (!empty($this->requisite)) {
            $query
                ->innerJoin(['ucs' => UserCloudSources::TABLE_NAME], 'u.site_id = ucs.site_id AND u.user_id = ucs.user_id')
                ->andWhere([
                    'ucs.type' => UserCloudSource::TYPE_REQUISITE,
                    'ucs.val' => $this->requisite,
                ]);
        } elseif (!empty($this->siteUser)) {
            $siteUsers = UserIdOrSiteUserIdMultilineValidator::explodeToSiteUserArrays($this->siteUser, $this);
            (new InBuilder($this->db, $siteUsers, ['site_id' => 'intval', 'user_id' => 'intval']))->filterInnerJoin($query);
        } elseif (!empty($this->cid)) {
            $query->andWhere(['u.cid' => $this->cid]);
        }

        $foundCid = $query->column();

        if (count($foundCid) === 0) {
            throw new NotFoundException('Cid not found');
        }

        return $foundCid;
    }

    private function searchFilter(): array
    {
        $filter = [];

        if (!empty($this->requisite)) {
            $filter['requisites'] = [$this->requisite];
        } elseif (!empty($this->siteUser)) {
            $filter['site_users'] = UserIdOrSiteUserIdMultilineValidator::explodeToSiteUserArrays($this->siteUser, $this);
        }

        return $filter;
    }

    private function checkCloudSizeLimit(array $foundCid): void
    {
        $cloudSize = (int) (new Query($this->db))->select("COUNT(*)")->from(Users::TABLE_NAME)->where(['cid' => $foundCid])->scalar();

        if ($cloudSize > self::MAX_CLOUD_SIZE) {
            throw new InvalidException("Cloud size {$cloudSize} is to big for display directly. Use 'Clusters' mode");
        }
    }
}
