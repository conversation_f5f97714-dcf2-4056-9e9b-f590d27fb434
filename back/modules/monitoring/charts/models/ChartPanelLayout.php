<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\models;

class ChartPanelLayout
{
    public int $rows;
    public int $cols;
    public array $charts;

    public function __construct(array $panelConfig)
    {
        TypeCastConfigureHelper::configure($this, $panelConfig);
    }

    /** @return Chart[][] */
    public function getChartsLayout(): array
    {
        $result = $this->getLayoutGrid();
        foreach ($result as $row => $cols) {
            foreach ($cols as $col => $config) {
                $result[$row][$col] = Chart::fromConfig($config);
            }
        }

        return $result;
    }

    public function getChartsLayoutConfig(): array
    {
        $result = [];
        foreach ($this->getChartsLayout() as $row => $cols) {
            foreach ($cols as $col => $chart) {
                $result[$row][$col] = [
                    'row_span' => $chart->row_span,
                    'col_span' => $chart->col_span,
                    'height' => $chart->height,
                    'refresh_period' => $chart->refresh_period,
                    'chartConfig' => $chart->getHighChartsConfig(),
                    'autoHeight' => $chart->getAutoHeight(),
                    'loadRange' => strtotime($chart->range, 0) * 1000, // Used only for initial data load
                ];
            }
        }

        return $result;
    }

    private function getLayoutGrid(): array
    {
        $result = [];
        $spans = [];

        for ($row = 0; $row < $this->rows; $row++) {
            $result[$row] = [];
            for ($col = 0; $col < $this->cols; $col++) {
                $chart = $this->charts[$row][$col] ?? Chart::noChartConfig();
                $result[$row][$col] = $chart;

                // Setting rows and cols needed to be removed because of row_span and col_span
                for ($row2span = 1; $row2span < $chart['row_span']; $row2span++) {
                    for ($col2span = 0; $col2span < $chart['col_span']; $col2span++) {
                        $spans[] = [$row + $row2span, $col + $col2span];
                    }
                }

                for ($col2span = 1; $col2span < $chart['col_span']; $col2span++) {
                    for ($row2span = 0; $row2span < $chart['row_span']; $row2span++) {
                        $spans[] = [$row + $row2span, $col + $col2span];
                    }
                }
            }
        }

        foreach ($spans as [$row, $col]) {
            unset($result[$row][$col]);
        }

        return $result;
    }
}
