<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\models;

use app\back\components\Container;
use app\back\components\FormGrid;
use app\back\components\helpers\Arr;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\SafeValidator;
use app\back\components\validators\StringArrayValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\components\validators\StringValidator;

class Chart
{
    use FormGrid;

    protected const int CHART_HEIGHT_UNIT = 300;

    public const int STACKING_NONE = 1;
    public const int STACKING_NORMAL = 2;
    public const int STACKING_PERCENT = 3;

    public const string TYPE_AREASPLINE = 'areaspline';
    public const string TYPE_AREA = 'area';
    public const string TYPE_SPLINE = 'spline';
    public const string TYPE_LINE = 'line';
    public const string TYPE_COLUMN = 'column';
    public const string TYPE_SCATTER = 'scatter';

    protected const string VISIBLE_TITLE = 'title';
    protected const string VISIBLE_LEGEND = 'legend';
    protected const string VISIBLE_BUTTONS = 'buttons';
    protected const string VISIBLE_RANGE = 'range';
    protected const string VISIBLE_SCROLL = 'scroll';
    protected const string VISIBLE_OVERVIEW = 'overview';

    public const array STACKINGS = [
        self::STACKING_NONE => 'None',
        self::STACKING_NORMAL => 'Normal',
        self::STACKING_PERCENT => 'Percent',
    ];

    public const array TYPES = [
        self::TYPE_AREASPLINE => 'Area (smooth)',
        self::TYPE_AREA => 'Area',
        self::TYPE_SPLINE => 'Line (smooth)',
        self::TYPE_LINE => 'Line',
        self::TYPE_COLUMN => 'Column',
        self::TYPE_SCATTER => 'Scatter',
    ];

    public const array RANGES = [
        '10 minutes', '30 minutes',
        '1 hour', '3 hours', '12 hours',
        '1 day', '2 days', '3 days',
        '1 week', '10 days', '2 weeks', '3 weeks', '4 weeks',
        '1 month', '2 months', '3 months',
    ];

    public const array VISIBLES = [
        self::VISIBLE_TITLE => 'Title',
        self::VISIBLE_LEGEND => 'Legend',
        self::VISIBLE_BUTTONS => 'Buttons',
        self::VISIBLE_RANGE => 'Range',
        self::VISIBLE_SCROLL => 'Scroll',
        self::VISIBLE_OVERVIEW => 'Overview',
    ];

    public const array DIVIDERS = [
        '1 month' => '1 day',
        '10 day' => '4 hour',
        '2 day' => '1 hour',
        '12 hours' => '30 minute',
        '3 hours' => '5 minute',
    ];

    #[StringValidator(0, 100)]
    public ?string $name = 'New chart';
    #[IntValidator(1, 12)]
    public int $row_span = 1;
    #[IntValidator(1, 12)]
    public int $col_span = 1;
    #[StringInArrayValidator(self::TYPES)]
    public string $type = self::TYPE_AREASPLINE;
    #[IntInArrayValidator(self::STACKINGS)]
    public ?int $stacking = self::STACKING_NONE;
    #[IntValidator]
    public int $height = 0;
    #[StringArrayValidator(self::VISIBLES)]
    public array $visible = [self::VISIBLE_TITLE];
    #[BooleanValidator]
    public ?bool $independent_y_axes = false;
    #[StringInArrayValidator(self::RANGES, true)]
    public string $range = '3 days';
    #[StringValidator(0, 100)]
    public string $default_range = '1 day';
    #[IntValidator(5, 60 * 24)]
    public ?int $refresh_period = 30;
    #[BooleanValidator]
    public ?bool $truncate_period = false;
    #[StringInArrayValidator(self::DIVIDERS, true)]
    public ?string $divider = null;
    #[IntValidator]
    public ?int $y_min = null;
    #[IntValidator]
    public ?int $y_max = null;
    #[SafeValidator]
    public array $series = [];

    public Container $container;

    public static function fromConfig(array $config): self
    {
        $chart = new self();
        TypeCastConfigureHelper::configure($chart, $config);
        return $chart;
    }

    /** @return Serie[] */
    public function series(): array
    {
        $result = [];

        foreach ($this->series as $serieIndex => $serieConfig) {
            if (empty($serieConfig)) {
                continue;
            }
            $result[$serieIndex] = Serie::fromConfig($serieConfig);
        }
        return $result;
    }

    public function serie(int $serieIndex): Serie
    {
        return $this->series()[$serieIndex];
    }

    protected function blocks(): array
    {
        return [
            [
                $this->textInputCell(7, 'name', 'Name'),
                $this->selectCell(2, 'type', 'Type', [
                    'list' => Arr::assocToIdName(self::TYPES),
                    'multiple' => false,
                ]),
                $this->selectCell(2, 'stacking', 'Stacking', [
                    'list' => Arr::assocToIdName(self::STACKINGS),
                    'multiple' => false,
                ]),
                $this->textInputCell(1, 'height', 'Height', ['hint' => 'pixel']),
            ],
            [
                $this->textInputCell(1, 'row_span', 'Row span'),
                $this->textInputCell(1, 'col_span', 'Col span'),
                $this->textInputCell(1, 'y_min', 'Y min'),
                $this->textInputCell(1, 'y_max', 'Y max'),
                $this->checkboxCell(2, 'independent_y_axes', 'Independent Y-Axes'),
                $this->listCell(6, 'visible', 'Show controls', [
                    'list' => Arr::assocToIdName(self::VISIBLES),
                    'multiple' => true,
                ]),
            ],
            [
                $this->selectCell(2, 'range', 'Range', [
                    'list' => Arr::columnToIdName(self::RANGES),
                    'multiple' => false,
                ]),
                $this->selectCell(2, 'default_range', 'Default range', [
                    'list' => Arr::columnToIdName(self::RANGES),
                    'multiple' => false,
                ]),
                $this->checkboxCell(2, 'truncate_period', 'Truncate period'),
                $this->selectCell(2, 'divider', 'Explicit group', [
                    'list' => Arr::columnToIdName(array_values(self::DIVIDERS)),
                    'multiple' => false,
                    'hint' => 'disables adaptive zoom'
                ]),
                $this->textInputCell(1, 'refresh_period', 'Refresh', ['hint' => 'minutes' ]),
            ],
        ];
    }

    public function getAutoHeight(): int
    {
        $h = $this->height === 0 ? null : $this->height;

        return $h ?? ($this->row_span * static::CHART_HEIGHT_UNIT);
    }

    private function isVisible(string $part): bool
    {
        return in_array($part, $this->visible, true);
    }

    public function getHighChartsConfig(): array
    {
        $series = $this->getSeriesConfig();

        if ($this->independent_y_axes) {
            $yAxes = [];
            foreach ($series as $k => &$serie) {
                $serie['yAxis'] = $k;
                $yAxes[] = [
                    'labels' => [
                        'style' => [
                            'color' => $serie['color'] ?? null
                        ]
                    ],
                    'min' => $this->y_min,
                    'max' => $this->y_max,
                    'tickPixelInterval' => 50,
                    'opposite' => false,
                ];
            }
            unset($serie);
        } else {
            $yAxes[] = [
                'min' => $this->y_min,
                'max' => $this->y_max,
                'tickPixelInterval' => 50,
                'opposite' => false,
            ];
        }

        return [
            'chart' => [
                'zooming' => [
                    'type' => 'x',
                    'mouseWheel' => [
                        'enabled' => false,
                    ],
                ],
                'time' => [
                    'timezone' => 'Europe/Kyiv',
                ],
                'events' => [
                    'redraw' => null
                ],
            ],
            'boost' => [
                'enabled' => false,
            ],
            'title' => [
                'text' => $this->isVisible(static::VISIBLE_TITLE) ? $this->name : null,
                'align' => 'left',
            ],
            'legend' => [
                'enabled' => $this->isVisible(static::VISIBLE_LEGEND),
                'align' => $this->isVisible(static::VISIBLE_TITLE) ? 'right' : 'left',
                'verticalAlign' => 'top',
                'y' =>  -15,
            ],
            'rangeSelector' => [
                'enabled' => $this->isVisible(static::VISIBLE_RANGE) || $this->isVisible(static::VISIBLE_BUTTONS),
                'inputEnabled' => $this->isVisible(static::VISIBLE_RANGE) ? null : false,
                'buttons' => $this->isVisible(static::VISIBLE_BUTTONS) ? [
                    ['type' => 'hour', 'count' => 1, 'text' => '1h'],
                    ['type' => 'hour', 'count' => 6, 'text' => '6h'],
                    ['type' => 'day', 'count' => 1, 'text' => '1d'],
                    ['type' => 'day', 'count' => 3, 'text' => '3d'],
                    ['type' => 'week', 'count' => 1, 'text' => '1w'],
                ] : [],
            ],
            'navigator' => [
                'enabled' => $this->isVisible(static::VISIBLE_OVERVIEW),
                'adaptToUpdatedData' => false,
                'series' => [[
                    'dataGrouping' => [
                        'enabled' => false,
                    ],
                ]],
                'xAxis' => [
                    'overscroll' => $this->truncate_period ? $this->getDivider(strtotime($this->range, 0)) * 1000 : 0,
                ],
            ],
            'scrollbar' => [
                'enabled' => $this->isVisible(static::VISIBLE_SCROLL),
                'liveRedraw' => false,
            ],
            'xAxis' => [[
                'ordinal' => false,
                'gridLineWidth' => 1,
                'tickWidth' => 0,
                'range' => strtotime($this->default_range, 0) * 1000,
                'minRange' => 1600 * 1000, // half hour
                'events' => (object)[],
            ]],
            'yAxis' => $yAxes,
            'series' => $series,
        ];
    }

    public function getSeriesData(Container $container, string $from, string $to, int $divider): array
    {
        $result = [];

        foreach ($this->series() as $serie) {
            $result[] = $serie->getData($container, $from, $to, $divider);
        }

        return $result;
    }

    private function getSeriesConfig(): array
    {
        $result = [];

        foreach ($this->series as $serieConfig) {
            $result[] = Serie::fromConfig($serieConfig)->getHighchartsConfig($this);
        }

        return $result;
    }

    public static function noChartConfig(): array
    {
        return [
            'name' => null,
            'row_span' => 1,
            'col_span' => 1,
        ];
    }

    public function fromToDivider(string $from, string $to): array
    {
        $period = strtotime($to) - strtotime($from);

        $divider = $this->getDivider($period);

        $now = strtotime(date('Y-m-d H:i:00'));

        $toCeil = (int) ceil(strtotime($to) / $divider) * $divider;

        if ($toCeil > $now && $this->truncate_period) {
            $toTime = (int) floor(strtotime($to) / $divider) * $divider;
        } else {
            $toTime = $toCeil;
        }

        $fromTime = (int) floor(($toTime - $period) / $divider) * $divider;

        return [date('Y-m-d H:i:s', $fromTime), date('Y-m-d H:i:s', $toTime), $divider];
    }

    public function getDivider(int $period): int
    {
        if (!empty($this->divider)) {
            return max(strtotime($this->divider, 0), 60); // Not less than 1 minute
        }

        foreach (static::DIVIDERS as $range => $divider) {
            if ($period > strtotime($range, 0)) {
                return strtotime($divider, 0);
            }
        }

        return strtotime('1 minute', 0);
    }
}
