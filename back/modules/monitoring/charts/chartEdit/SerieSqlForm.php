<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\chartEdit;

use app\back\components\Container;
use app\back\components\Form;
use app\back\components\helpers\DateHelper;
use app\back\components\validators\IntValidator;
use app\back\modules\monitoring\charts\panel\PanelRowColValidatorTrait;

class SerieSqlForm
{
    use Form;
    use PanelRowColValidatorTrait;

    #[IntValidator]
    public int $serieIndex;

    public function sql(Container $container): string
    {
        $chart = $this->panel->chart($this->row, $this->col);
        $to = time();
        $from = strtotime('-' . $chart->default_range, $to);

        return $chart->serie($this->serieIndex)->getSql($container, ...$chart->fromToDivider(date(DateHelper::DATETIME_FORMAT_PHP, $from), date(DateHelper::DATETIME_FORMAT_PHP, $to)));
    }
}
