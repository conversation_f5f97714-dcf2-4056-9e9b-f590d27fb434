<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\chartEdit;

use app\back\components\Form;
use app\back\components\validators\IntValidator;
use app\back\modules\monitoring\charts\panel\PanelRowColValidatorTrait;

class SerieCloneForm
{
    use Form;
    use PanelRowColValidatorTrait;

    #[IntValidator]
    public int $serieIndex;

    public function clone(): void
    {
        $serie = $this->panel->chart($this->row, $this->col)->serie($this->serieIndex);
        $serie->name = 'Copy of ' . $serie->name;
        $this->panel->config = $this->panel->updatedChartSerieConfig($this->row, $this->col, null, $serie);
        $this->chartPanelsRepo->update($this->panel, ['config']);
    }
}
