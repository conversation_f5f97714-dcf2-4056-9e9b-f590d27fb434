<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\chartEdit;

use app\back\components\Form;
use app\back\components\validators\IntValidator;
use app\back\modules\monitoring\charts\panel\PanelRowColValidatorTrait;

class SerieDeleteForm
{
    use Form;
    use PanelRowColValidatorTrait;

    #[IntValidator]
    public int $serieIndex;

    public function delete(): void
    {
        $this->panel->config = $this->panel->updatedChartSerieConfig($this->row, $this->col, $this->serieIndex, null);
        $this->chartPanelsRepo->update($this->panel, ['config']);
    }
}
