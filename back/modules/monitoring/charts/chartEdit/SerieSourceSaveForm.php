<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\chartEdit;

use app\back\components\Container;
use app\back\components\exceptions\InvalidException;
use app\back\components\Form;
use app\back\components\validators\CallableValidator;
use app\back\components\validators\IntValidator;
use app\back\modules\monitoring\charts\panel\PanelRowColValidatorTrait;
use app\back\modules\monitoring\charts\sources\BaseChartSource;

class SerieSourceSaveForm
{
    use Form;
    use PanelRowColValidatorTrait;

    #[IntValidator]
    #[CallableValidator([self::class, 'sourceLoad'])]
    public int $serieIndex;
    #[CallableValidator([self::class, 'validateSourceConfig'])]
    public array $chartConfig;

    private BaseChartSource $source;

    public function __construct(
        private readonly Container $container,
    ) {
    }

    public static function sourceLoad(int $value, self $form): ?string
    {
        $form->source = $form->panel->chart($form->row, $form->col)->serie($value)->getChartSource($form->container);
        return null;
    }

    public static function validateSourceConfig(mixed $value, self $form): ?string
    {
        if (!isset($form->source)) {
            return 'Source config not loaded';
        }

        $form->source->load($value);
        $valid = $form->source->validate();
        if (!$valid) {
            throw new InvalidException(['errors' => $form->source->getFirstErrors()]);
        }

        return null;
    }

    public function save(): void
    {
        $this->panel->config = $this->panel->updatedChartSerieSourceConfig($this->row, $this->col, $this->serieIndex, $this->source);
        $this->chartPanelsRepo->update($this->panel, ['config']);
    }
}
