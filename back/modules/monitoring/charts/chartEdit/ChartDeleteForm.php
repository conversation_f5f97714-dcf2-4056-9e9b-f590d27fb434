<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\chartEdit;

use app\back\components\Form;
use app\back\modules\monitoring\charts\models\Chart;
use app\back\modules\monitoring\charts\panel\PanelRowColValidatorTrait;

class ChartDeleteForm
{
    use Form;
    use PanelRowColValidatorTrait;

    public function delete(): void
    {
        $this->panel->config['charts'][$this->row][$this->col] = Chart::noChartConfig();
        $this->chartPanelsRepo->update($this->panel, ['config']);
    }
}
