<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\chartEdit;

use app\back\components\accessCheck\AccessCheckPage;
use app\back\components\accessCheck\AccessCheckRole;
use app\back\components\accessCheck\AccessCheckSecureDictRequest;
use app\back\components\Container;
use app\back\components\Permission;
use app\back\components\Request;
use app\back\components\SessionMessages;
use app\back\components\WebController;
use app\back\modules\monitoring\charts\panel\PanelController;
use app\back\repositories\ChartPanels;

#[AccessCheckPage]
#[AccessCheckSecureDictRequest(ChartPanels::class, 'JSON.panelId')]
#[AccessCheckRole(Permission::PERM_CHARTS_EDIT)]
class ChartEditController extends WebController
{
    public function resourceName(): string
    {
        return PanelController::RESOURCE_NAME;
    }

    public function actionChartEditForm(ChartViewForm $form, Request $request): array
    {
        $form->validateOrException($request->json());

        return $form->chart->response();
    }

    public function actionChartAddForm(ChartAddForm $form, Request $request): array
    {
        return $form->validateAndResponse($request->json());
    }

    public function actionChartAdd(ChartAddForm $form, Request $request, SessionMessages $messages): array
    {
        $form->validateOrException($request->json());
        $form->add();
        $messages->success('Chart successfully created');
        $this->bl()->modify($form->params());
        return $form->response();
    }

    public function actionChartDelete(ChartDeleteForm $form, Request $request, SessionMessages $messages): void
    {
        $form->validateOrException($request->json());
        $form->delete();
        $messages->success('Chart deleted');
        $this->bl()->delete($form->params());
    }

    public function actionChartSave(ChartSaveForm $form, Request $request, SessionMessages $messages): void
    {
        $form->validateOrException($request->json());
        $form->save();
        $messages->success('Chart settings saved');
        $this->bl()->modify($form->params());
    }

    public function actionHighChartsConfig(ChartHighChartsConfigForm $form, Request $request): array
    {
        $form->validateOrException($request->json());

        return $form->getHighChartsConfig();
    }

    public function actionChartSeriesForms(SeriesFormsForm $form, Request $request): array
    {
        $form->validateOrException($request->json());
        return $form->complexResponse();
    }

    public function actionChartSwitch(ChartSwitchForm $form, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->switch();
    }

    public function actionSerieAddForm(SerieAddForm $form): array
    {
        return $form->response();
    }

    public function actionSerieAdd(Container $container, SerieAddForm $form, Request $request, SessionMessages $messages): array
    {
        $form->validateOrException($request->json());
        $form->add($container);
        $messages->success('New serie added');
        $this->bl()->modify($form->params());
        return $form->response();
    }

    public function actionSerieDelete(SerieDeleteForm $form, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->delete();
    }

    public function actionSerieClone(SerieCloneForm $form, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->clone();
    }

    public function actionSerieSave(SerieSaveForm $form, Request $request): array
    {
        $form->validateOrException($request->json());
        $form->save();
        $this->bl()->modify($form->params());
        return $form->response();
    }

    public function actionSerieSourceSave(SerieSourceSaveForm $form, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->save();
        $this->bl()->modify($form->params());
    }

    #[AccessCheckRole(Permission::PERM_VIEW_SQL)]
    public function actionSerieSql(Container $container, SerieSqlForm $form, Request $request): array
    {
        $form->validateOrException($request->json());
        return ['sql' => $form->sql($container)];
    }
}
