<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\chartEdit;

use app\back\components\Form;
use app\back\components\validators\CallableValidator;
use app\back\components\validators\IntValidator;
use app\back\modules\monitoring\charts\models\Serie;
use app\back\modules\monitoring\charts\panel\PanelRowColValidatorTrait;

class SerieSaveForm
{
    use Form;
    use PanelRowColValidatorTrait;

    #[IntValidator]
    public int $serieIndex;
    #[CallableValidator([self::class, 'validateConfig'])]
    public array $config = [];

    private array $configErrors = [];

    public static function validateConfig(mixed $value): ?string
    {
        $serie = new Serie();
        $serie->validateOrException($value);
        return null;
    }

    public function save(): void
    {
        $serie = $this->panel->chart($this->row, $this->col)->serie($this->serieIndex);
        $updatedSerie = Serie::fromConfig(array_merge($serie->getAttributes(), $this->config));

        $this->panel->config = $this->panel->updatedChartSerieConfig($this->row, $this->col, $this->serieIndex, $updatedSerie);
        $this->chartPanelsRepo->update($this->panel, ['config']);
    }

    public function response(): array
    {
        return $this->panel->chart($this->row, $this->col)->serie($this->serieIndex)->response();
    }
}
