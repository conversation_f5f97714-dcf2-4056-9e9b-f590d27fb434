<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\chartEdit;

use app\back\components\Form;
use app\back\modules\monitoring\charts\panel\PanelRowColValidatorTrait;

class ChartHighChartsConfigForm
{
    use Form;
    use PanelRowColValidatorTrait;

    public function getHighChartsConfig(): array
    {
        return $this->panel->chart($this->row, $this->col)->getHighChartsConfig();
    }
}
