<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\chartEdit;

use app\back\components\FormGrid;
use app\back\components\helpers\Arr;
use app\back\components\validators\CallableValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\modules\monitoring\charts\models\Chart;
use app\back\modules\monitoring\charts\panel\PanelRowColValidatorTrait;

class ChartAddForm
{
    use FormGrid;
    use PanelRowColValidatorTrait;

    private const int MODE_NEW = 1;
    private const int MODE_COPY = 2;

    #[IntInArrayValidator([self::MODE_NEW, self::MODE_COPY], true)]
    public int $mode = self::MODE_NEW;
    #[CallableValidator([self::class, 'fromChartValidation'])]
    public ?string $fromChart = null;

    public static function fromChartValidation($value, self $form): ?string
    {
        if (empty($value) && $form->mode === self::MODE_COPY) {
            return "required";
        }

        if (!empty($value) && !array_key_exists($value, $form->availableCharts())) {
            return 'Invalid chart';
        }

        return null;
    }

    protected function blocks(): array
    {
        return [
            [
                $this->multipleSubmit(4, 'mode', [
                    'list' => Arr::assocToIdName([
                        static::MODE_NEW => 'Create empty chart',
                        static::MODE_COPY => 'Copy from available →',
                    ]),
                ]),
                $this->selectCell(8, 'fromChart', 'Available charts', [
                    'type' => 'select',
                    'multiple' => false,
                    'list' => Arr::assocToIdName($this->availableCharts()),
                ]),
            ],
        ];
    }

    public function add(): void
    {

        if (!empty($this->panel->config['charts'][$this->row][$this->col]['name'])) {
            return;
        }

        $chartConfig = Chart::noChartConfig();

        if ($this->mode === static::MODE_COPY) {
            [$fromRow, $fromCol] = explode('-', $this->fromChart);
            $chartConfig = $this->panel->config['charts'][$fromRow][$fromCol] ?? [];

            if (empty($chartConfig['name'])) {
                // Empty chart found
                return;
            }
            $chartConfig['name'] = 'Copy of ' . $chartConfig['name'];
        } else {
            $chartConfig['name'] = 'New chart';
        }


        $this->panel->config = $this->panel->updatedConfigChart($this->row, $this->col, $chartConfig);
        $this->chartPanelsRepo->update($this->panel, ['config']);
    }

    private function availableCharts(): array
    {
        foreach ($this->panel->config['charts'] as $row => $cols) {
            foreach ($cols as $col => $chart) {
                if (empty($chart['name'])) {
                    continue;
                }

                $result[$row . '-' . $col] = $chart['name'];
            }
        }

        return $result ?? [];
    }
}
