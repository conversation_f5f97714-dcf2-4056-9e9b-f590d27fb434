<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\chartEdit;

use app\back\components\exceptions\ExpectationFailedException;
use app\back\components\Form;
use app\back\components\validators\IntValidator;
use app\back\modules\monitoring\charts\models\Chart;
use app\back\modules\monitoring\charts\panel\PanelRowColValidatorTrait;

class ChartSwitchForm
{
    use Form;
    use PanelRowColValidatorTrait;

    #[IntValidator]
    public int $targetCol;
    #[IntValidator]
    public int $targetRow;

    public function switch(): void
    {
        $config = $this->panel->config;

        $targetChart = $config['charts'][$this->targetRow][$this->targetCol] ?? Chart::noChartConfig();
        $sourceChart = $config['charts'][$this->row][$this->col] ?? Chart::noChartConfig();

        if (
            (int)$sourceChart['col_span'] !== (int)$targetChart['col_span'] ||
            (int)$sourceChart['row_span'] !== (int)$targetChart['row_span']
        ) {
            throw new ExpectationFailedException('Failed to switch charts: col or row spans must be equal ');
        }

        $config['charts'][$this->targetRow][$this->targetCol] = $sourceChart;
        $config['charts'][$this->row][$this->col] = $targetChart;

        $this->panel->config = $config;
        $this->chartPanelsRepo->update($this->panel, ['config']);
    }
}
