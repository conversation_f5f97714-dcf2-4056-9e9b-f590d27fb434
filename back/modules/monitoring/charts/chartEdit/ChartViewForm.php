<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\chartEdit;

use app\back\components\Form;
use app\back\modules\monitoring\charts\models\Chart;
use app\back\modules\monitoring\charts\panel\PanelRowColValidatorTrait;

class ChartViewForm
{
    use Form {
        validateOrException as formValidateOrException;
    }
    use PanelRowColValidatorTrait;

    public Chart $chart;

    public function validateOrException(array $requestData): void
    {
        $this->formValidateOrException($requestData);
        $this->chart = $this->panel->chart($this->row, $this->col);
    }
}
