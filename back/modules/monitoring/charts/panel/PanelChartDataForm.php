<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\panel;

use app\back\components\BaseAuthAccess;
use app\back\components\Container;
use app\back\components\exceptions\InvalidException;
use app\back\components\Form;
use app\back\components\helpers\Db;
use app\back\components\helpers\Str;
use app\back\components\SecondaryConnection;
use app\back\components\validators\IntValidator;

class PanelChartDataForm
{
    use Form;
    use PanelRowColValidatorTrait;

    #[IntValidator]
    public int $from;
    #[IntValidator]
    public int $to;

    public function __construct(
        private readonly SecondaryConnection $db,
    ) {
    }

    public function data(Container $container): array
    {
        $chart = $this->panel->chart($this->row, $this->col);
        $ldap = Str::emailToLdap($container->get(BaseAuthAccess::class)->employee()->email);
        Db::setApplicationName($this->db, "monitoring/panel/{$this->panelId}/{$this->row}/{$this->col} ($ldap)");

        return $chart->getSeriesData($container, ...$chart->fromToDivider(...$this->fromTo()));
    }

    private function fromTo(): array
    {
        $from = date('Y-m-d H:i:00', (int) floor($this->from / 1000));

        $toTimeLimited = floor($this->to / 1000 / 60) * 60 > time() ? time() : floor($this->to / 1000);

        $to = date('Y-m-d H:i:00', (int) $toTimeLimited);

        $fromTimestamp = strtotime($from);
        $toTimestamp = strtotime($to);

        if ($toTimestamp < $fromTimestamp) {
            throw new InvalidException("Period is negative");
        }

        if ($toTimestamp > strtotime('+100 days', $fromTimestamp)) {
            throw new InvalidException("Period too big from $from to $to");
        }

        return [$from, $to];
    }
}
