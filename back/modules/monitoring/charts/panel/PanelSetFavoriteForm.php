<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\panel;

use app\back\components\BaseAuthAccess;
use app\back\components\Form;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\CallableValidator;
use app\back\entities\ChartFavorite;
use app\back\repositories\ChartFavorites;
use app\back\repositories\ChartPanels;

class PanelSetFavoriteForm
{
    use Form;

    #[CallableValidator([self::class, 'checkPanelExists'])]
    public int $panelId;
    #[BooleanValidator]
    public bool $favorite;

    public function __construct(
        private readonly ChartPanels $chartPanelsRepo,
        private readonly ChartFavorites $chartFavoritesRepo,
        private readonly BaseAuthAccess $auth,
    ) {
    }

    public function updateFavorite(): void
    {
        $entity = $this->chartFavoritesRepo->findOne(['panel_id' => $this->panelId, 'employee_id' => $this->auth->employeeId()]);
        if ($this->favorite && $entity === null) {
            $entity = new ChartFavorite(['panel_id' => $this->panelId, 'employee_id' => $this->auth->employeeId()]);
            $this->chartFavoritesRepo->insert($entity);
        } elseif (!$this->favorite) {
            $this->chartFavoritesRepo->delete($entity);
        }
    }

    public static function checkPanelExists(mixed $value, self $form): ?string
    {
        $panel = $form->chartPanelsRepo->findOne(['id' => $value]);
        if ($panel === null) {
            return "Panel #$value does not exist";
        }
        return null;
    }
}
