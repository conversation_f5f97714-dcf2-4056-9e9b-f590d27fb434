<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\panel;

use app\back\components\AccessChecker;
use app\back\components\BaseAuthAccess;
use app\back\components\FormGrid;
use app\back\components\validators\StringValidator;
use app\back\entities\ChartPanel;
use app\back\repositories\AuthAssignments;
use app\back\repositories\ChartPanels;

class PanelAddForm
{
    use FormGrid;

    #[StringValidator(3, 100)]
    public string $name;

    public function __construct(
        private readonly BaseAuthAccess $authAccess,
        private readonly ChartPanels $chartPanelsRepo,
        private readonly AccessChecker $accessChecker,
        private readonly AuthAssignments $authAssignmentsRepo,
    ) {
    }

    protected function blocks(): array
    {
        return [
            [
                $this->textInputCell(9, 'name', 'Name'),
                $this->submitCell(3, 'Add', [
                    'buttonIcon' => 'icn-plus',
                    'buttonStyle' => 'btn-success',
                ]),
            ],
        ];
    }

    public function add(): int
    {
        $employeeId = $this->authAccess->employeeId();

        $entity = new ChartPanel($this->params());
        $entity->config = ChartPanel::defaultChartPanelConfig();
        $entity->created_by = $employeeId;
        $this->chartPanelsRepo->insert($entity);

        $this->authAssignmentsRepo->assign($employeeId, [$this->chartPanelsRepo->permissionPrefix() . $entity->id]);
        $this->accessChecker->clearCache($employeeId);

        return $entity->id;
    }
}
