<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\panel;

use app\back\components\Initializable;
use app\back\components\validators\CallableValidator;
use app\back\components\validators\IdValidator;
use app\back\entities\ChartPanel;
use app\back\repositories\ChartPanels;

trait PanelIdValidatorTrait
{
    #[IdValidator]
    #[CallableValidator([self::class, 'loadPanel'])]
    public int $panelId;

    private readonly ChartPanels $chartPanelsRepo;
    private readonly ChartPanel $panel;

    #[Initializable]
    final public function initChartPanels(ChartPanels $chartPanelsRepo): void
    {
        $this->chartPanelsRepo = $chartPanelsRepo;
    }

    public static function loadPanel(int $value, self $form): ?string
    {
        /** @noinspection PhpFieldAssignmentTypeMismatchInspection */
        $form->panel = $form->chartPanelsRepo->findOneOr404(['id' => $value]);
        return null;
    }
}
