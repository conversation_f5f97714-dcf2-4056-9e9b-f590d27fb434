<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\panel;

use app\back\components\accessCheck\AccessCheckPage;
use app\back\components\accessCheck\AccessCheckRole;
use app\back\components\accessCheck\AccessCheckSecureDictRequest;
use app\back\components\Container;
use app\back\components\Permission;
use app\back\components\Request;
use app\back\components\SessionMessages;
use app\back\components\WebController;
use app\back\repositories\ChartPanels;

#[AccessCheckPage]
class PanelController extends WebController
{
    public const string RESOURCE_NAME = '/monitoring/charts';

    public function resourceName(): string
    {
        return static::RESOURCE_NAME;
    }

    public function actionList(PanelsListForm $form, Request $request): array
    {
        $response = $form->validateAndResponse($request->json());
        $this->bl()->open();

        return $response;
    }

    #[AccessCheckSecureDictRequest(ChartPanels::class, 'ROUTE.panelId')]
    public function actionView(PanelViewForm $form, Request $request): array
    {
        $form->validateOrException($request->routeParams());
        return $form->response();
    }

    #[AccessCheckSecureDictRequest(ChartPanels::class, 'JSON.panelId')]
    public function actionChartData(Container $container, PanelChartDataForm $form, Request $request): array
    {
        $form->validateOrException($request->json());
        return $form->data($container);
    }

    #[AccessCheckRole(Permission::PERM_CHARTS_CREATE)]
    public function actionAddForm(PanelAddForm $form): array
    {
        return $form->response();
    }

    #[AccessCheckRole(Permission::PERM_CHARTS_CREATE)]
    public function actionAdd(PanelAddForm $form, Request $request, SessionMessages $messages): void
    {
        $form->validateOrException($request->json());
        $form->add();
        $messages->success('Panel successfully created');
        $this->bl()->create($form->params());
    }

    #[AccessCheckSecureDictRequest(ChartPanels::class, 'JSON.panelId')]
    public function actionSetFavorite(PanelSetFavoriteForm $form, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->updateFavorite();
    }
}
