<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\panelEdit;

use app\back\components\accessCheck\AccessCheckPage;
use app\back\components\accessCheck\AccessCheckRole;
use app\back\components\accessCheck\AccessCheckSecureDictRequest;
use app\back\components\Permission;
use app\back\components\Request;
use app\back\components\SessionMessages;
use app\back\components\WebController;
use app\back\modules\monitoring\charts\panel\PanelController;
use app\back\repositories\ChartPanels;

#[AccessCheckPage]
#[AccessCheckRole(Permission::PERM_CHARTS_EDIT)]
#[AccessCheckSecureDictRequest(ChartPanels::class, 'JSON.panelId')]
class PanelEditController extends WebController
{
    public function resourceName(): string
    {
        return PanelController::RESOURCE_NAME;
    }

    public function actionFormAndLayout(PanelSaveForm $form, Request $request): array
    {
        $form->validateOrException($request->json(), ['panelId']);
        return $form->complexResponse();
    }

    public function actionSave(PanelSaveForm $form, Request $request, SessionMessages $messages): void
    {
        $form->validateOrException($request->json());
        $form->save();
        $messages->success('Panel settings saved');
        $this->bl()->modify($form->params());
    }

    public function actionDelete(PanelDeleteForm $form, Request $request, SessionMessages $messages): void
    {
        $form->validateOrException($request->json());
        $form->delete();
        $this->bl()->delete(['id' => $form->panelId]);
        $messages->success('Panel successfully deleted');
    }
}
