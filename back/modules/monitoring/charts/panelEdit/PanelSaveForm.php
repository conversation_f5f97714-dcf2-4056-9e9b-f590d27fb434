<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\panelEdit;

use app\back\components\BaseAuthAccess;
use app\back\components\FormGrid;
use app\back\components\helpers\Arr;
use app\back\components\Permission;
use app\back\components\validators\CallableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\ChartPanel;
use app\back\modules\monitoring\charts\models\ChartPanelLayout;
use app\back\repositories\ChartPanels;

class PanelSaveForm
{
    use FormGrid;

    #[IdValidator]
    #[CallableValidator([self::class, 'panelLoadValidator'])]
    public int $panelId;
    #[StringValidator(3, 100)]
    public string $name;
    #[IntValidator(1, 12)]
    #[CallableValidator([self::class, 'checkPaneCell'], 'rows')]
    public int $rows;
    #[IntValidator(1, 12)]
    #[CallableValidator([self::class, 'checkPaneCell'], 'cols')]
    public int $cols;

    private ChartPanel $panel;

    public function __construct(
        public readonly ChartPanels $chartPanelsRepo,
        public readonly BaseAuthAccess $auth,
    ) {
    }

    public static function panelLoadValidator(int $value, self $form): ?string
    {
        /** @noinspection PhpFieldAssignmentTypeMismatchInspection */
        $form->panel = $form->chartPanelsRepo->findOneOr404(['id' => $value]);

        return null;
    }

    public static function checkPaneCell(mixed $value, self $form, array $context, string $name): ?string
    {
        foreach ($form->panel->config['charts'] as $row => $cols) {
            foreach ($cols as $col => $chart) {
                if (empty($chart['name'])) {
                    continue;
                }

                if (($name === 'cols' && $col >= $context['cols']) || ($name === 'rows' && $row >= $context['rows'])) {
                    return "Can't decrease $name with not empty cells";
                }
            }
        }
        return null;
    }

    protected function blocks(): array
    {
        return [
            [
                $this->textInputCell(7, 'name', 'Name'),
                $this->textInputCell(2, 'cols', 'Cols'),
                $this->textInputCell(2, 'rows', 'Rows'),
                $this->submitCell(1, 'Save'),
            ],
        ];
    }

    public function complexResponse(): array
    {
        Arr::configure($this, [
            'name' => $this->panel->name,
            'cols' => (int)$this->panel->config['cols'],
            'rows' => (int)$this->panel->config['rows']
        ]);

        return [
            'form' => $this->response(),
            'layout' => (new ChartPanelLayout($this->panel->config))->getChartsLayout(),
            'showSqlButton' => $this->auth->can(Permission::PERM_VIEW_SQL),
        ];
    }

    public function save(): void
    {
        $this->panel->name = $this->name;

        $config = $this->panel->config;
        $config['rows'] = $this->rows;
        $config['cols'] = $this->cols;
        $this->panel->config = $config;

        $this->chartPanelsRepo->update($this->panel, ['name', 'config']);
    }
}
