<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Lotteries;

use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\QueryParamsBag;
use app\back\modules\reports\columns\Decorated;
use app\back\modules\reports\columns\decorators\YesNoDecorator;
use app\back\modules\reports\columns\Selected;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;

class LotteryTicketIsGoldColumn extends BaseColumn implements Selected, Decorated
{
    use YesNoDecorator;

    public string $title = 'Is gold';
    public string $column = 'is_gold';

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string|Expression
    {
        return $this->columnExpression();
    }
}
