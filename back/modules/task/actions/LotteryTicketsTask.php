<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\modules\task\ImportTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\BaseRepository;
use app\back\repositories\LotteryTickets;

class LotteryTicketsTask extends ImportTask
{
    use TaskWithFromToRequest;

    public function __construct(
        private readonly LotteryTickets $lotteryTicketsRepo,
        private readonly TaskSiteIdResolver $siteIdResolver,
    ) {
    }

    protected function repository(): BaseRepository
    {
        return $this->lotteryTicketsRepo;
    }

    protected function beforeFind(array &$row): bool
    {
        $row['site_id'] = $this->siteIdResolver->siteId();

        return true;
    }
}
