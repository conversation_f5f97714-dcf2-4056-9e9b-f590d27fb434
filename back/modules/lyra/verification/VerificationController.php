<?php

namespace app\back\modules\lyra\verification;

use app\back\components\accessCheck\AccessCheckPage;
use app\back\components\Request;
use app\back\components\WebController;

#[AccessCheckPage]
class VerificationController extends WebController
{
    public function actionData(ChartForm $form, Request $request): array
    {
        $form->validateOrException($request->json());
        return $form->getFinancialActivityChart();
    }
}
