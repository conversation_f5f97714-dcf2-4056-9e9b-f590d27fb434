<?php

declare(strict_types=1);

namespace app\tests\unit\repositories;

use app\back\repositories\UserComments;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class UsersCommentsTest extends TestCase
{
    use DbTransactionalUnitTrait;

    #[DataProvider('commentsDataProvider')]
    public function testNormalizeComment(string $productComment, string $expectedComment): void
    {
        /** @var UserComments $repo */
        $repo = $this->repo(UserComments::class);
        $normalizedComment = $repo->normalizeComment($productComment);
        self::assertSame($expectedComment, $normalizedComment);
    }

    public static function CommentsDataProvider(): array
    {
        return [
            [
                'Locked. Comment: Перманент лок #65267156 #65267339 #65267515 #65267591 #65267659 #65267725 #65267924 #65267974 #65268072 #65268174 #65269271',
                'Locked. Comment: Перманент лок [list from 11 ids]',
            ],
            [
                'Locked. Comment: Перманент лок 65267156 65267339 65267515 65267591 65267659 65267725 65267924 65267974 65268072 65268174 65269271',
                'Locked. Comment: Перманент лок [list from 11 ids]',
            ],
            [
                'Locked. Comment: Перманент лок 65267156 65267339 65267515',
                'Locked. Comment: Перманент лок 65267156 65267339 65267515',
            ],
            [
                'Simple short comment',
                'Simple short comment',
            ],
        ];
    }
}
