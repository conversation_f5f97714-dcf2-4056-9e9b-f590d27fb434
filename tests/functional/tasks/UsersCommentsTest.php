<?php

declare(strict_types=1);

namespace app\tests\functional\tasks;

use app\back\entities\Site;
use app\back\repositories\UserComments;
use app\tests\libs\ApiUnitTrait;
use app\tests\libs\DbTransactionalUnitTrait;

class UsersCommentsTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;
    use ApiUnitTrait;

    private const string DATA = <<<DATA
id,player_id,email,comment,status,updated_at,archived_at
111,11111111,<EMAIL>,Locked. Comment: Перманент лок 65267156 65267339 65267515,visible,2019-12-16T00:00:00+00:00,

DATA;

    public function testAddGiComment(): void
    {
        $this->runTask('users-comments', 'VV', $this->debugFile(static::DATA));

        $this->seeRecord(UserComments::class, ['user_id' => 11111111, 'site_id' => Site::VV, 'comment' => 'Locked. Comment: Перманент лок 65267156 65267339 65267515']);
    }
}
